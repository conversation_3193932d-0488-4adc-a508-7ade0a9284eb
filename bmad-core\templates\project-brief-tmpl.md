# Project Brief: {{Project Name}}

[[LLM: This template guides creation of a comprehensive Project Brief that serves as the foundational input for product development. 

Start by asking the user which mode they prefer:
1. **Interactive Mode** - Work through each section collaboratively
2. **YOLO Mode** - Generate complete draft for review and refinement

Before beginning, understand what inputs are available (brainstorming results, market research, competitive analysis, initial ideas) and gather project context.]]

## Executive Summary

[[LLM: Create a concise overview that captures the essence of the project. Include:
- Product concept in 1-2 sentences
- Primary problem being solved
- Target market identification
- Key value proposition]]

{{Write executive summary based on information gathered}}

## Problem Statement

[[LLM: Articulate the problem with clarity and evidence. Address:
- Current state and pain points
- Impact of the problem (quantify if possible)
- Why existing solutions fall short
- Urgency and importance of solving this now]]

{{Detailed problem description with supporting evidence}}

## Proposed Solution

[[LLM: Describe the solution approach at a high level. Include:
- Core concept and approach
- Key differentiators from existing solutions
- Why this solution will succeed where others haven't
- High-level vision for the product]]

{{Solution description focusing on the "what" and "why", not implementation details}}

## Target Users

[[LLM: Define and characterize the intended users with specificity. For each user segment include:
- Demographic/firmographic profile
- Current behaviors and workflows
- Specific needs and pain points
- Goals they're trying to achieve]]

### Primary User Segment: {{Segment Name}}
{{Detailed description of primary users}}

### Secondary User Segment: {{Segment Name}}
{{Description of secondary users if applicable}}

## Goals & Success Metrics

[[LLM: Establish clear objectives and how to measure success. Make goals SMART (Specific, Measurable, Achievable, Relevant, Time-bound)]]

### Business Objectives
- {{Objective 1 with metric}}
- {{Objective 2 with metric}}
- {{Objective 3 with metric}}

### User Success Metrics
- {{How users will measure value}}
- {{Engagement metrics}}
- {{Satisfaction indicators}}

### Key Performance Indicators (KPIs)
- {{KPI 1: Definition and target}}
- {{KPI 2: Definition and target}}
- {{KPI 3: Definition and target}}

## MVP Scope

[[LLM: Define the minimum viable product clearly. Be specific about what's in and what's out. Help user distinguish must-haves from nice-to-haves.]]

### Core Features (Must Have)
- **Feature 1:** {{Brief description and why it's essential}}
- **Feature 2:** {{Brief description and why it's essential}}
- **Feature 3:** {{Brief description and why it's essential}}

### Out of Scope for MVP
- {{Feature/capability explicitly not in MVP}}
- {{Feature/capability to be considered post-MVP}}

### MVP Success Criteria
{{Define what constitutes a successful MVP launch}}

## Post-MVP Vision

[[LLM: Outline the longer-term product direction without overcommitting to specifics]]

### Phase 2 Features
{{Next priority features after MVP success}}

### Long-term Vision
{{Where this product could go in 1-2 years}}

### Expansion Opportunities
{{Potential new markets, use cases, or integrations}}

## Technical Considerations

[[LLM: Document known technical constraints and preferences. Note these are initial thoughts, not final decisions.]]

### Platform Requirements
- **Target Platforms:** {{Web, mobile, desktop, etc.}}
- **Browser/OS Support:** {{Specific requirements}}
- **Performance Requirements:** {{Load times, concurrent users, etc.}}

### Technology Preferences
- **Frontend:** {{If any preferences exist}}
- **Backend:** {{If any preferences exist}}
- **Database:** {{If any preferences exist}}
- **Hosting/Infrastructure:** {{Cloud preferences, on-prem requirements}}

### Architecture Considerations
- **Repository Structure:** {{Initial thoughts on monorepo vs. polyrepo}}
- **Service Architecture:** {{Initial thoughts on monolith vs. microservices}}
- **Integration Requirements:** {{Third-party services, APIs}}
- **Security/Compliance:** {{Any specific requirements}}

## Constraints & Assumptions

[[LLM: Clearly state limitations and assumptions to set realistic expectations]]

### Constraints
- **Budget:** {{If known}}
- **Timeline:** {{Target launch date or development timeframe}}
- **Resources:** {{Team size, skill constraints}}
- **Technical:** {{Legacy systems, required tech stack}}

### Key Assumptions
- {{Assumption about users, market, or technology}}
- {{Assumption about resources or support}}
- {{Assumption about external dependencies}}

## Risks & Open Questions

[[LLM: Identify unknowns and potential challenges proactively]]

### Key Risks
- **Risk 1:** {{Description and potential impact}}
- **Risk 2:** {{Description and potential impact}}
- **Risk 3:** {{Description and potential impact}}

### Open Questions
- {{Question needing research or decision}}
- {{Question about technical approach}}
- {{Question about market or users}}

### Areas Needing Further Research
- {{Topic requiring deeper investigation}}
- {{Validation needed before proceeding}}

## Appendices

### A. Research Summary
{{If applicable, summarize key findings from:
- Market research
- Competitive analysis  
- User interviews
- Technical feasibility studies}}

### B. Stakeholder Input
{{Key feedback or requirements from stakeholders}}

### C. References
{{Links to relevant documents, research, or examples}}

## Next Steps

### Immediate Actions
1. {{First concrete next step}}
2. {{Second concrete next step}}
3. {{Third concrete next step}}

### PM Handoff

This Project Brief provides the full context for {{Project Name}}. Please start in 'PRD Generation Mode', review the brief thoroughly to work with the user to create the PRD section by section as the template indicates, asking for any necessary clarification or suggesting improvements.

---

[[LLM: After completing each major section (not subsections), offer advanced elicitation with these custom options for project briefs:

**Project Brief Elicitation Actions**
0. Expand section with more specific details
1. Validate against similar successful products
2. Stress test assumptions with edge cases
3. Explore alternative solution approaches
4. Analyze resource/constraint trade-offs
5. Generate risk mitigation strategies
6. Challenge scope from MVP minimalist view
7. Brainstorm creative feature possibilities
8. If only we had [resource/capability/time]...
9. Proceed to next section

These replace the standard elicitation options when working on project brief documents.]]
