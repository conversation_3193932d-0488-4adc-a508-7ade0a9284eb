# {{service_name}} Service Brief

[[LLM: This template creates a detailed brief for an individual microservice within a distributed system. Focus on the service's bounded context, responsibilities, and integration points. Use advanced elicitation if service details are unclear.]]

## Service Overview

### Purpose & Scope
[[LLM: Define the service's primary purpose and bounded context]]

**Service Purpose**: {{service_purpose}}

**Bounded Context**: {{bounded_context}}

**Business Capabilities**: {{business_capabilities}}

### Service Characteristics
[[LLM: Describe the service's key characteristics and patterns]]

- **Service Type**: {{service_type}} (Domain Service / Infrastructure Service / Gateway Service)
- **Data Ownership**: {{data_ownership}}
- **Team Ownership**: {{team_ownership}}
- **Deployment Independence**: {{deployment_independence}}

## Functional Requirements

### Core Responsibilities
[[LLM: Define what this service is responsible for]]

<<REPEAT responsibility>>
- **{{responsibility_name}}**: {{responsibility_description}}
  - Input: {{responsibility_input}}
  - Output: {{responsibility_output}}
  - Business Rules: {{responsibility_rules}}
<<END_REPEAT>>

### Service Boundaries
[[LLM: Clearly define what is inside and outside this service's scope]]

**Inside Service Scope**:
- {{inside_scope_1}}
- {{inside_scope_2}}
- {{inside_scope_3}}

**Outside Service Scope** (handled by other services):
- {{outside_scope_1}}
- {{outside_scope_2}}
- {{outside_scope_3}}

## API Design

### Service Interface
[[LLM: Define the service's external API]]

**API Style**: {{api_style}} (REST / GraphQL / gRPC / Event-driven)

**Base URL**: {{base_url}}

### Core Endpoints
[[LLM: Define the main API endpoints]]

<<REPEAT endpoint>>
- **{{http_method}} {{endpoint_path}}**
  - Purpose: {{endpoint_purpose}}
  - Request: {{endpoint_request}}
  - Response: {{endpoint_response}}
  - Status Codes: {{endpoint_status_codes}}
<<END_REPEAT>>

### Events Published
[[LLM: Define events this service publishes]]

^^CONDITION: publishes_events^^
<<REPEAT published_event>>
- **{{event_name}}**
  - Trigger: {{event_trigger}}
  - Payload: {{event_payload}}
  - Consumers: {{event_consumers}}
<<END_REPEAT>>
^^END_CONDITION^^

### Events Consumed
[[LLM: Define events this service consumes]]

^^CONDITION: consumes_events^^
<<REPEAT consumed_event>>
- **{{consumed_event_name}}**
  - Source: {{event_source}}
  - Handler: {{event_handler}}
  - Side Effects: {{event_side_effects}}
<<END_REPEAT>>
^^END_CONDITION^^

## Data Model

### Core Entities
[[LLM: Define the main data entities owned by this service]]

<<REPEAT entity>>
- **{{entity_name}}**
  - Purpose: {{entity_purpose}}
  - Key Attributes: {{entity_attributes}}
  - Relationships: {{entity_relationships}}
  - Lifecycle: {{entity_lifecycle}}
<<END_REPEAT>>

### Data Storage
[[LLM: Define data storage strategy]]

- **Database Type**: {{database_type}}
- **Schema Design**: {{schema_design}}
- **Data Consistency**: {{data_consistency}}
- **Backup Strategy**: {{backup_strategy}}

### Data Access Patterns
[[LLM: Describe how data is accessed and queried]]

- **Read Patterns**: {{read_patterns}}
- **Write Patterns**: {{write_patterns}}
- **Query Optimization**: {{query_optimization}}
- **Caching Strategy**: {{caching_strategy}}

## Service Dependencies

### Upstream Dependencies
[[LLM: Services this service depends on]]

<<REPEAT upstream_dependency>>
- **{{upstream_service_name}}**
  - Purpose: {{dependency_purpose}}
  - Interface: {{dependency_interface}}
  - Failure Handling: {{dependency_failure_handling}}
  - SLA Requirements: {{dependency_sla}}
<<END_REPEAT>>

### Downstream Consumers
[[LLM: Services that depend on this service]]

<<REPEAT downstream_consumer>>
- **{{consumer_service_name}}**
  - Usage: {{consumer_usage}}
  - SLA Provided: {{consumer_sla}}
  - Interface: {{consumer_interface}}
<<END_REPEAT>>

### External Integrations
[[LLM: External systems this service integrates with]]

^^CONDITION: has_external_integrations^^
<<REPEAT external_integration>>
- **{{external_system_name}}**
  - Purpose: {{integration_purpose}}
  - Protocol: {{integration_protocol}}
  - Authentication: {{integration_auth}}
  - Error Handling: {{integration_error_handling}}
<<END_REPEAT>>
^^END_CONDITION^^

## Non-Functional Requirements

### Performance Requirements
[[LLM: Define performance expectations]]

- **Response Time**: {{response_time_target}}
- **Throughput**: {{throughput_target}}
- **Concurrent Users**: {{concurrent_users}}
- **Data Volume**: {{data_volume}}

### Scalability Requirements
[[LLM: Define scaling needs]]

- **Scaling Strategy**: {{scaling_strategy}}
- **Scaling Triggers**: {{scaling_triggers}}
- **Resource Limits**: {{resource_limits}}
- **Bottlenecks**: {{potential_bottlenecks}}

### Reliability Requirements
[[LLM: Define reliability and availability needs]]

- **Availability Target**: {{availability_target}}
- **Error Rate Target**: {{error_rate_target}}
- **Recovery Time**: {{recovery_time}}
- **Fault Tolerance**: {{fault_tolerance_strategy}}

### Security Requirements
[[LLM: Define security needs specific to this service]]

- **Authentication**: {{authentication_method}}
- **Authorization**: {{authorization_strategy}}
- **Data Encryption**: {{encryption_requirements}}
- **Audit Logging**: {{audit_requirements}}

## Implementation Considerations

### Technology Stack
[[LLM: Define the technology choices for this service]]

- **Runtime/Framework**: {{runtime_framework}}
- **Database**: {{database_choice}}
- **Caching**: {{caching_technology}}
- **Message Queue**: {{message_queue}}

### Development Guidelines
[[LLM: Define development standards and practices]]

- **Code Structure**: {{code_structure}}
- **Testing Strategy**: {{testing_strategy}}
- **Documentation**: {{documentation_requirements}}
- **Monitoring**: {{monitoring_requirements}}

### Deployment Strategy
[[LLM: Define how this service will be deployed]]

- **Deployment Method**: {{deployment_method}}
- **Environment Strategy**: {{environment_strategy}}
- **Configuration Management**: {{config_management}}
- **Health Checks**: {{health_checks}}

## Quality Assurance

### Testing Strategy
[[LLM: Define testing approach for this service]]

- **Unit Testing**: {{unit_testing_approach}}
- **Integration Testing**: {{integration_testing_approach}}
- **Contract Testing**: {{contract_testing_approach}}
- **Performance Testing**: {{performance_testing_approach}}

### Monitoring & Observability
[[LLM: Define monitoring and observability requirements]]

- **Metrics**: {{key_metrics}}
- **Logging**: {{logging_strategy}}
- **Tracing**: {{tracing_requirements}}
- **Alerting**: {{alerting_strategy}}

## Migration Strategy

^^CONDITION: is_migration_from_monolith^^
### Migration from Monolith
[[LLM: Define migration strategy if extracting from existing system]]

- **Migration Approach**: {{migration_approach}}
- **Data Migration**: {{data_migration_strategy}}
- **Rollback Plan**: {{rollback_strategy}}
- **Validation Strategy**: {{migration_validation}}
^^END_CONDITION^^

## Risks & Mitigation

### Technical Risks
[[LLM: Identify technical risks specific to this service]]

<<REPEAT technical_risk>>
- **Risk**: {{risk_description}}
  - **Impact**: {{risk_impact}}
  - **Mitigation**: {{risk_mitigation}}
<<END_REPEAT>>

---

@{example_service_brief}
# Product Catalog Service Brief

## Service Overview

### Purpose & Scope
**Service Purpose**: Manage product information, inventory, and search capabilities for the e-commerce platform

**Bounded Context**: Product domain including catalog management, inventory tracking, and product search

**Business Capabilities**: Product CRUD operations, inventory management, search and filtering, product recommendations

### Service Characteristics
- **Service Type**: Domain Service
- **Data Ownership**: Owns all product and inventory data
- **Team Ownership**: Product Team
- **Deployment Independence**: Fully independent deployment

## API Design

### Core Endpoints
- **GET /products**
  - Purpose: List products with filtering and pagination
  - Request: Query parameters for filters, sorting, pagination
  - Response: Product list with metadata
  - Status Codes: 200, 400, 500

- **POST /products**
  - Purpose: Create new product
  - Request: Product data in JSON
  - Response: Created product with ID
  - Status Codes: 201, 400, 409, 500

### Events Published
- **ProductCreated**
  - Trigger: New product added to catalog
  - Payload: Product ID, basic product data
  - Consumers: Search Service, Recommendation Service

- **InventoryUpdated**
  - Trigger: Product inventory changes
  - Payload: Product ID, new inventory level
  - Consumers: Order Service, Notification Service
@{/example_service_brief}

[[LLM: Process all template markup and create a comprehensive service brief. Use the example as guidance but create content specific to the user's service. Ensure all sections are complete and actionable.]]
