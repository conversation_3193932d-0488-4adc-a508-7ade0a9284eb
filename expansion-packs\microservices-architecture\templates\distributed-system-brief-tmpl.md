# {{system_name}} - Distributed System Brief

[[LLM: This template creates a comprehensive system-level brief for distributed applications following the Enhanced Microservices Architecture framework from Architecture_stack/Microservices_Architecture.md. Focus on organic system thinking, value stream orientation, and the three pillars of communication. Use advanced elicitation to gather requirements if information is missing.]]

## Executive Summary

[[LLM: Create a 2-3 paragraph executive summary covering the business problem, proposed distributed system solution, and key architectural decisions. Focus on why a distributed architecture is needed.]]

**Business Problem**: {{business_problem}}

**Distributed System Solution**: {{solution_overview}}

**Key Architectural Decisions**: {{key_decisions}}

## System Overview

### Business Context
[[LLM: Describe the business domain and why a distributed architecture is appropriate]]

- **Domain**: {{business_domain}}
- **Scale Requirements**: {{scale_requirements}}
- **Team Structure**: {{team_structure}}
- **Complexity Drivers**: {{complexity_drivers}}

### System Boundaries
[[LLM: Define the overall system boundaries and external integrations]]

- **System Scope**: {{system_scope}}
- **External Systems**: {{external_integrations}}
- **Data Sources**: {{data_sources}}
- **User Types**: {{user_types}}

## Service Architecture Strategy

### Service Decomposition Approach
[[LLM: Explain the strategy for breaking down the system into services following Architecture_stack patterns]]

- **Decomposition Strategy**: {{decomposition_strategy}}
  - Domain-driven design with bounded contexts (following DDD principles from Architecture_stack)
  - Value stream orientation: organizing services around business value streams and customer journeys
  - Business capability alignment with end-to-end responsibility and continuous flow
  - Team ownership alignment following Conway's Law and human-centered design principles
  - Data ownership patterns with polyglot persistence (each service owns its data completely)
  - Transaction boundaries with ACID within services, eventual consistency across services
  - Organic system thinking: services as living organisms with adaptive learning and self-healing properties
  - Evolutionary architecture with fitness functions and incremental change capabilities

### Service Categories
[[LLM: Categorize the types of services in the system]]

<<REPEAT service_category>>
- **{{category_name}}**: {{category_description}}
  - Purpose: {{category_purpose}}
  - Examples: {{category_examples}}
  - Characteristics: {{category_characteristics}}
<<END_REPEAT>>

## Cross-Service Patterns

### Communication Patterns
[[LLM: Define how services will communicate following the three pillars of microservices communication from Architecture_stack]]

- **Pillar 1 - Synchronous Communication**: {{sync_patterns}}
  - REST APIs for request-response patterns and CRUD operations
  - GraphQL for flexible data queries and mobile/web clients
  - gRPC for high-performance internal service communication

- **Pillar 2 - Asynchronous Communication**: {{async_patterns}}
  - Message queues (RabbitMQ, AWS SQS) for reliable task processing
  - Pub/sub patterns for event notifications and loose coupling

- **Pillar 3 - Event Streaming**: {{event_streaming_patterns}}
  - Apache Kafka for event sourcing and real-time data streams
  - Event-driven architecture for system-wide state changes
  - CQRS patterns for read/write separation

### Data Management Strategy
[[LLM: Describe data consistency and management across services following Architecture_stack patterns]]

- **Data Ownership**: {{data_ownership_strategy}} (Each service owns its data completely - no shared databases)
- **Polyglot Persistence**: {{polyglot_persistence_strategy}} (Right database for each service's needs)
  - Relational databases for ACID transactions and structured data
  - NoSQL databases for scale and flexible schemas (document, key-value, graph)
  - Time-series databases for metrics and monitoring data
  - Search engines for full-text search and analytics
- **Consistency Models**: {{consistency_patterns}}
  - Strong consistency within service boundaries (ACID transactions)
  - Eventual consistency across service boundaries with conflict resolution
  - Causal consistency for related events and collaborative applications
- **Advanced Data Patterns**: {{advanced_data_patterns}}
  - Event Sourcing: immutable event log as source of truth with temporal queries
  - CQRS Implementation: separate read and write models with independent scaling
  - Materialized Views: precomputed results for efficient queries
  - Saga Pattern: distributed transaction coordination with compensation

### Organic System Design
[[LLM: Describe how the system will exhibit organic characteristics following Architecture_stack principles]]

- **Adaptive Learning**: {{adaptive_learning_strategy}}
  - System evolution based on usage patterns and feedback
  - Continuous intelligence with real-time analytics and predictive capabilities
  - Automated decision-making based on operational metrics
- **Self-Healing Properties**: {{self_healing_strategy}}
  - Automatic detection and recovery from failures
  - Circuit breakers and bulkhead patterns for failure isolation
  - Health monitoring with automatic service restart and replacement
- **Evolutionary Growth**: {{evolutionary_growth_strategy}}
  - Fitness functions to verify architectural characteristics
  - Incremental change patterns rather than big-bang rewrites
  - Support for experimentation without disrupting the entire system

### Human-Centered Design Considerations
[[LLM: Address human-centered design requirements following Architecture_stack principles]]

- **Accessibility Requirements**: {{accessibility_requirements}}
  - Universal design principles for all user abilities
  - Assistive technology integration support
  - Compliance with accessibility standards (WCAG, Section 508)
- **Ethical AI Integration**: {{ethical_ai_strategy}}
  - Prevention of algorithmic bias in system decisions
  - Transparency in automated decision-making processes
  - Human oversight for critical system decisions
- **Privacy Preservation**: {{privacy_strategy}}
  - Data minimization and purpose limitation principles
  - User consent management and right to be forgotten
  - Privacy by design in system architecture

### Cross-Cutting Concerns
[[LLM: Address system-wide concerns that span multiple services]]

- **Authentication & Authorization**: {{auth_strategy}}
- **Logging & Monitoring**: {{observability_strategy}}
- **Configuration Management**: {{config_strategy}}
- **Error Handling**: {{error_handling_strategy}}

## Frontend Architecture

### Microfrontend Strategy
[[LLM: Define the frontend modularity approach]]

^^CONDITION: has_frontend^^
- **Frontend Architecture**: {{frontend_architecture}}
- **Module Boundaries**: {{frontend_modules}}
- **Shared Components**: {{shared_components}}
- **Routing Strategy**: {{routing_strategy}}
^^END_CONDITION^^

## Technology Stack

### Core Technologies
[[LLM: Define the primary technology choices following Architecture_stack recommendations]]

- **Container Orchestration**: {{container_orchestration}} (Kubernetes with 92% market dominance)
- **Service Mesh**: {{service_mesh}} (Istio for comprehensive features, Linkerd for lightweight, Consul Connect for security)
- **API Gateway**: {{api_gateway}} (Kong, Istio Gateway, AWS API Gateway with rate limiting and security)
- **Event Backbone**: {{event_backbone}} (Apache Kafka for event streaming, AWS Kinesis, Azure Event Hubs)
- **Message Broker**: {{message_broker}} (RabbitMQ for reliable messaging, AWS SQS for cloud-native)
- **Database Strategy**: {{database_strategy}} (Polyglot persistence: SQL for ACID, NoSQL for scale, time-series for metrics)

### Infrastructure & Deployment
[[LLM: Define deployment and infrastructure patterns]]

- **Container Strategy**: {{container_strategy}}
- **Orchestration**: {{orchestration_platform}}
- **Service Discovery**: {{service_discovery}}
- **Load Balancing**: {{load_balancing}}
- **Monitoring Stack**: {{monitoring_stack}}

## Quality Attributes

### Scalability Requirements
[[LLM: Define scalability needs and patterns]]

- **Expected Load**: {{expected_load}}
- **Scaling Patterns**: {{scaling_patterns}}
- **Performance Targets**: {{performance_targets}}

### Reliability Requirements
[[LLM: Define reliability and resilience needs]]

- **Availability Targets**: {{availability_targets}}
- **Fault Tolerance**: {{fault_tolerance_patterns}}
- **Disaster Recovery**: {{disaster_recovery_strategy}}

### Security Requirements
[[LLM: Define security patterns and requirements]]

- **Security Model**: {{security_model}}
- **Data Protection**: {{data_protection}}
- **Compliance Requirements**: {{compliance_requirements}}

## Development & Operations

### Team Organization
[[LLM: Describe how teams will be organized around services]]

- **Team Structure**: {{team_organization}}
- **Service Ownership**: {{service_ownership}}
- **Shared Responsibilities**: {{shared_responsibilities}}

### Development Workflow
[[LLM: Define development and deployment processes]]

- **Development Process**: {{development_process}}
- **Testing Strategy**: {{testing_strategy}}
- **Deployment Pipeline**: {{deployment_pipeline}}
- **Release Coordination**: {{release_coordination}}

## Risk Assessment

### Technical Risks
[[LLM: Identify and assess technical risks]]

<<REPEAT technical_risk>>
- **Risk**: {{risk_description}}
  - **Impact**: {{risk_impact}}
  - **Probability**: {{risk_probability}}
  - **Mitigation**: {{risk_mitigation}}
<<END_REPEAT>>

### Organizational Risks
[[LLM: Identify and assess organizational risks]]

<<REPEAT organizational_risk>>
- **Risk**: {{org_risk_description}}
  - **Impact**: {{org_risk_impact}}
  - **Mitigation**: {{org_risk_mitigation}}
<<END_REPEAT>>

## Next Steps

### Immediate Actions
[[LLM: Define the next steps for moving forward with the distributed system]]

1. {{next_step_1}}
2. {{next_step_2}}
3. {{next_step_3}}

### Phase Planning
[[LLM: Outline the phases for implementing the distributed system]]

- **Phase 1**: {{phase_1_description}}
- **Phase 2**: {{phase_2_description}}
- **Phase 3**: {{phase_3_description}}

---

@{example_system_brief}
# E-commerce Platform - Distributed System Brief

## Executive Summary

**Business Problem**: Our monolithic e-commerce platform is struggling to scale with growing traffic and team size. Development velocity has decreased, and we need to support multiple sales channels with different requirements.

**Distributed System Solution**: Decompose the monolith into domain-aligned microservices enabling independent team development, technology diversity, and horizontal scaling.

**Key Architectural Decisions**: Event-driven architecture for order processing, API gateway for external access, microfrontends for different customer touchpoints.

## Service Architecture Strategy

### Service Decomposition Approach
- **Decomposition Strategy**: Domain-driven design with bounded contexts
  - Customer Management (user accounts, profiles)
  - Product Catalog (inventory, search, recommendations)
  - Order Management (cart, checkout, fulfillment)
  - Payment Processing (transactions, billing)
  - Notification Service (email, SMS, push)

### Communication Patterns
- **Synchronous Communication**: REST APIs for real-time queries, GraphQL for mobile apps
- **Asynchronous Communication**: Event streaming for order processing, pub/sub for notifications
@{/example_system_brief}

[[LLM: Process all template markup and create a comprehensive distributed system brief. Use the example as guidance but create content specific to the user's system. Ensure all sections are complete and actionable.]]
