# {{system_name}} - Distributed System Brief

[[LLM: This template creates a comprehensive system-level brief for distributed applications. Focus on system-wide concerns, service boundaries, and cross-cutting patterns. Use advanced elicitation to gather requirements if information is missing.]]

## Executive Summary

[[LLM: Create a 2-3 paragraph executive summary covering the business problem, proposed distributed system solution, and key architectural decisions. Focus on why a distributed architecture is needed.]]

**Business Problem**: {{business_problem}}

**Distributed System Solution**: {{solution_overview}}

**Key Architectural Decisions**: {{key_decisions}}

## System Overview

### Business Context
[[LLM: Describe the business domain and why a distributed architecture is appropriate]]

- **Domain**: {{business_domain}}
- **Scale Requirements**: {{scale_requirements}}
- **Team Structure**: {{team_structure}}
- **Complexity Drivers**: {{complexity_drivers}}

### System Boundaries
[[LLM: Define the overall system boundaries and external integrations]]

- **System Scope**: {{system_scope}}
- **External Systems**: {{external_integrations}}
- **Data Sources**: {{data_sources}}
- **User Types**: {{user_types}}

## Service Architecture Strategy

### Service Decomposition Approach
[[LLM: Explain the strategy for breaking down the system into services]]

- **Decomposition Strategy**: {{decomposition_strategy}}
  - Domain-driven design boundaries
  - Team ownership alignment
  - Data ownership patterns
  - Transaction boundaries

### Service Categories
[[LLM: Categorize the types of services in the system]]

<<REPEAT service_category>>
- **{{category_name}}**: {{category_description}}
  - Purpose: {{category_purpose}}
  - Examples: {{category_examples}}
  - Characteristics: {{category_characteristics}}
<<END_REPEAT>>

## Cross-Service Patterns

### Communication Patterns
[[LLM: Define how services will communicate with each other]]

- **Synchronous Communication**: {{sync_patterns}}
  - REST APIs for {{sync_use_cases}}
  - GraphQL for {{graphql_use_cases}}
  - gRPC for {{grpc_use_cases}}

- **Asynchronous Communication**: {{async_patterns}}
  - Event streaming for {{event_use_cases}}
  - Message queues for {{queue_use_cases}}
  - Pub/sub for {{pubsub_use_cases}}

### Data Management Strategy
[[LLM: Describe data consistency and management across services]]

- **Data Ownership**: {{data_ownership_strategy}}
- **Consistency Patterns**: {{consistency_patterns}}
- **Shared Data Handling**: {{shared_data_strategy}}
- **Event Sourcing**: {{event_sourcing_usage}}

### Cross-Cutting Concerns
[[LLM: Address system-wide concerns that span multiple services]]

- **Authentication & Authorization**: {{auth_strategy}}
- **Logging & Monitoring**: {{observability_strategy}}
- **Configuration Management**: {{config_strategy}}
- **Error Handling**: {{error_handling_strategy}}

## Frontend Architecture

### Microfrontend Strategy
[[LLM: Define the frontend modularity approach]]

^^CONDITION: has_frontend^^
- **Frontend Architecture**: {{frontend_architecture}}
- **Module Boundaries**: {{frontend_modules}}
- **Shared Components**: {{shared_components}}
- **Routing Strategy**: {{routing_strategy}}
^^END_CONDITION^^

## Technology Stack

### Core Technologies
[[LLM: Define the primary technology choices for the distributed system]]

- **Service Runtime**: {{service_runtime}}
- **API Gateway**: {{api_gateway}}
- **Service Mesh**: {{service_mesh}}
- **Message Broker**: {{message_broker}}
- **Database Strategy**: {{database_strategy}}

### Infrastructure & Deployment
[[LLM: Define deployment and infrastructure patterns]]

- **Container Strategy**: {{container_strategy}}
- **Orchestration**: {{orchestration_platform}}
- **Service Discovery**: {{service_discovery}}
- **Load Balancing**: {{load_balancing}}
- **Monitoring Stack**: {{monitoring_stack}}

## Quality Attributes

### Scalability Requirements
[[LLM: Define scalability needs and patterns]]

- **Expected Load**: {{expected_load}}
- **Scaling Patterns**: {{scaling_patterns}}
- **Performance Targets**: {{performance_targets}}

### Reliability Requirements
[[LLM: Define reliability and resilience needs]]

- **Availability Targets**: {{availability_targets}}
- **Fault Tolerance**: {{fault_tolerance_patterns}}
- **Disaster Recovery**: {{disaster_recovery_strategy}}

### Security Requirements
[[LLM: Define security patterns and requirements]]

- **Security Model**: {{security_model}}
- **Data Protection**: {{data_protection}}
- **Compliance Requirements**: {{compliance_requirements}}

## Development & Operations

### Team Organization
[[LLM: Describe how teams will be organized around services]]

- **Team Structure**: {{team_organization}}
- **Service Ownership**: {{service_ownership}}
- **Shared Responsibilities**: {{shared_responsibilities}}

### Development Workflow
[[LLM: Define development and deployment processes]]

- **Development Process**: {{development_process}}
- **Testing Strategy**: {{testing_strategy}}
- **Deployment Pipeline**: {{deployment_pipeline}}
- **Release Coordination**: {{release_coordination}}

## Risk Assessment

### Technical Risks
[[LLM: Identify and assess technical risks]]

<<REPEAT technical_risk>>
- **Risk**: {{risk_description}}
  - **Impact**: {{risk_impact}}
  - **Probability**: {{risk_probability}}
  - **Mitigation**: {{risk_mitigation}}
<<END_REPEAT>>

### Organizational Risks
[[LLM: Identify and assess organizational risks]]

<<REPEAT organizational_risk>>
- **Risk**: {{org_risk_description}}
  - **Impact**: {{org_risk_impact}}
  - **Mitigation**: {{org_risk_mitigation}}
<<END_REPEAT>>

## Next Steps

### Immediate Actions
[[LLM: Define the next steps for moving forward with the distributed system]]

1. {{next_step_1}}
2. {{next_step_2}}
3. {{next_step_3}}

### Phase Planning
[[LLM: Outline the phases for implementing the distributed system]]

- **Phase 1**: {{phase_1_description}}
- **Phase 2**: {{phase_2_description}}
- **Phase 3**: {{phase_3_description}}

---

@{example_system_brief}
# E-commerce Platform - Distributed System Brief

## Executive Summary

**Business Problem**: Our monolithic e-commerce platform is struggling to scale with growing traffic and team size. Development velocity has decreased, and we need to support multiple sales channels with different requirements.

**Distributed System Solution**: Decompose the monolith into domain-aligned microservices enabling independent team development, technology diversity, and horizontal scaling.

**Key Architectural Decisions**: Event-driven architecture for order processing, API gateway for external access, microfrontends for different customer touchpoints.

## Service Architecture Strategy

### Service Decomposition Approach
- **Decomposition Strategy**: Domain-driven design with bounded contexts
  - Customer Management (user accounts, profiles)
  - Product Catalog (inventory, search, recommendations)
  - Order Management (cart, checkout, fulfillment)
  - Payment Processing (transactions, billing)
  - Notification Service (email, SMS, push)

### Communication Patterns
- **Synchronous Communication**: REST APIs for real-time queries, GraphQL for mobile apps
- **Asynchronous Communication**: Event streaming for order processing, pub/sub for notifications
@{/example_system_brief}

[[LLM: Process all template markup and create a comprehensive distributed system brief. Use the example as guidance but create content specific to the user's system. Ensure all sections are complete and actionable.]]
