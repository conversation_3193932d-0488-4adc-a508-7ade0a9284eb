# Brainstorming Techniques Task

This task provides a comprehensive toolkit of creative brainstorming techniques for ideation and innovative thinking. The analyst can use these techniques to facilitate productive brainstorming sessions with users.

## Process

### 1. Session Setup

[[LLM: Begin by understanding the brainstorming context and goals. Ask clarifying questions if needed to determine the best approach.]]

1. **Establish Context**
   - Understand the problem space or opportunity area
   - Identify any constraints or parameters
   - Determine session goals (divergent exploration vs. focused ideation)

2. **Select Technique Approach**
   - Option A: User selects specific techniques
   - Option B: Analyst recommends techniques based on context
   - Option C: Random technique selection for creative variety
   - Option D: Progressive technique flow (start broad, narrow down)

### 2. Core Brainstorming Techniques

#### Creative Expansion Techniques

1. **"What If" Scenarios**
   [[LLM: Generate provocative what-if questions that challenge assumptions and expand thinking beyond current limitations.]]
   - What if we had unlimited resources?
   - What if this problem didn't exist?
   - What if we approached this from a child's perspective?
   - What if we had to solve this in 24 hours?

2. **Analogical Thinking**
   [[LLM: Help user draw parallels between their challenge and other domains, industries, or natural systems.]]
   - "How might this work like [X] but for [Y]?"
   - Nature-inspired solutions (biomimicry)
   - Cross-industry pattern matching
   - Historical precedent analysis

3. **Reversal/Inversion**
   [[LLM: Flip the problem or approach it from the opposite angle to reveal new insights.]]
   - What if we did the exact opposite?
   - How could we make this problem worse? (then reverse)
   - Start from the end goal and work backward
   - Reverse roles or perspectives

4. **First Principles Thinking**
   [[LLM: Break down to fundamental truths and rebuild from scratch.]]
   - What are the absolute fundamentals here?
   - What assumptions can we challenge?
   - If we started from zero, what would we build?
   - What laws of physics/economics/human nature apply?

#### Structured Ideation Frameworks

5. **SCAMPER Method**
   [[LLM: Guide through each SCAMPER prompt systematically.]]
   - **S**ubstitute: What can be substituted?
   - **C**ombine: What can be combined or integrated?
   - **A**dapt: What can be adapted from elsewhere?
   - **M**odify/Magnify: What can be emphasized or reduced?
   - **P**ut to other uses: What else could this be used for?
   - **E**liminate: What can be removed or simplified?
   - **R**everse/Rearrange: What can be reversed or reordered?

6. **Six Thinking Hats**
   [[LLM: Cycle through different thinking modes, spending focused time in each.]]
   - White Hat: Facts and information
   - Red Hat: Emotions and intuition
   - Black Hat: Caution and critical thinking
   - Yellow Hat: Optimism and benefits
   - Green Hat: Creativity and alternatives
   - Blue Hat: Process and control

7. **Mind Mapping**
   [[LLM: Create text-based mind maps with clear hierarchical structure.]]
   ```
   Central Concept
   ├── Branch 1
   │   ├── Sub-idea 1.1
   │   └── Sub-idea 1.2
   ├── Branch 2
   │   ├── Sub-idea 2.1
   │   └── Sub-idea 2.2
   └── Branch 3
       └── Sub-idea 3.1
   ```

#### Collaborative Techniques

8. **"Yes, And..." Building**
   [[LLM: Accept every idea and build upon it without judgment. Encourage wild ideas and defer criticism.]]
   - Accept the premise of each idea
   - Add to it with "Yes, and..."
   - Build chains of connected ideas
   - Explore tangents freely

9. **Brainwriting/Round Robin**
   [[LLM: Simulate multiple perspectives by generating ideas from different viewpoints.]]
   - Generate ideas from stakeholder perspectives
   - Build on previous ideas in rounds
   - Combine unrelated ideas
   - Cross-pollinate concepts

10. **Random Stimulation**
    [[LLM: Use random words, images, or concepts as creative triggers.]]
    - Random word association
    - Picture/metaphor inspiration
    - Forced connections between unrelated items
    - Constraint-based creativity

#### Deep Exploration Techniques

11. **Five Whys**
    [[LLM: Dig deeper into root causes and underlying motivations.]]
    - Why does this problem exist? → Answer → Why? (repeat 5 times)
    - Uncover hidden assumptions
    - Find root causes, not symptoms
    - Identify intervention points

12. **Morphological Analysis**
    [[LLM: Break down into parameters and systematically explore combinations.]]
    - List key parameters/dimensions
    - Identify possible values for each
    - Create combination matrix
    - Explore unusual combinations

13. **Provocation Technique (PO)**
    [[LLM: Make deliberately provocative statements to jar thinking.]]
    - PO: Cars have square wheels
    - PO: Customers pay us to take products
    - PO: The problem solves itself
    - Extract useful ideas from provocations

### 3. Technique Selection Guide

[[LLM: Help user select appropriate techniques based on their needs.]]

**For Initial Exploration:**
- What If Scenarios
- First Principles
- Mind Mapping

**For Stuck/Blocked Thinking:**
- Random Stimulation
- Reversal/Inversion
- Provocation Technique

**For Systematic Coverage:**
- SCAMPER
- Morphological Analysis
- Six Thinking Hats

**For Deep Understanding:**
- Five Whys
- Analogical Thinking
- First Principles

**For Team/Collaborative Settings:**
- Brainwriting
- "Yes, And..."
- Six Thinking Hats

### 4. Session Flow Management

[[LLM: Guide the brainstorming session with appropriate pacing and technique transitions.]]

1. **Warm-up Phase** (5-10 min)
   - Start with accessible techniques
   - Build creative confidence
   - Establish "no judgment" atmosphere

2. **Divergent Phase** (20-30 min)
   - Use expansion techniques
   - Generate quantity over quality
   - Encourage wild ideas

3. **Convergent Phase** (15-20 min)
   - Group and categorize ideas
   - Identify patterns and themes
   - Select promising directions

4. **Synthesis Phase** (10-15 min)
   - Combine complementary ideas
   - Refine and develop concepts
   - Prepare summary of insights

### 5. Output Format

[[LLM: Present brainstorming results in an organized, actionable format.]]

**Session Summary:**
- Techniques used
- Number of ideas generated
- Key themes identified

**Idea Categories:**
1. **Immediate Opportunities** - Ideas that could be implemented now
2. **Future Innovations** - Ideas requiring more development
3. **Moonshots** - Ambitious, transformative ideas
4. **Insights & Learnings** - Key realizations from the session

**Next Steps:**
- Which ideas to explore further
- Recommended follow-up techniques
- Suggested research areas

## Important Notes

- Maintain energy and momentum throughout the session
- Defer judgment - all ideas are valid during generation
- Quantity leads to quality - aim for many ideas
- Build on ideas collaboratively
- Document everything - even "silly" ideas can spark breakthroughs
- Take breaks if energy flags
- End with clear next actions