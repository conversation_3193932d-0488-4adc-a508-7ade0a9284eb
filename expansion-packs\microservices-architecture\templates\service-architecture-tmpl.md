# {{service_name}} Service - Technical Architecture

[[LLM: This template creates detailed technical architecture for an individual microservice. Focus on implementation details, technology choices, and service-specific patterns. Use advanced elicitation to gather technical requirements.]]

## Service Overview

### Service Identity
[[LLM: Define the service's technical identity and characteristics]]

**Service Name**: {{service_name}}

**Service Type**: {{service_type}} (Domain Service / Infrastructure Service / Gateway Service)

**Bounded Context**: {{bounded_context}}

**Team Ownership**: {{team_ownership}}

**Technology Stack**: {{primary_technology_stack}}

### Architecture Principles
[[LLM: Define architecture principles specific to this service]]

- **Single Responsibility**: {{single_responsibility_principle}}
- **Data Ownership**: {{data_ownership_principle}}
- **API-First Design**: {{api_first_principle}}
- **Scalability**: {{scalability_principle}}
- **Resilience**: {{resilience_principle}}

## Technology Stack

### Runtime Environment
[[LLM: Define the service runtime and framework choices]]

**Programming Language**: {{programming_language}}

**Framework**: {{framework_choice}}

**Runtime Version**: {{runtime_version}}

**Justification**: {{technology_justification}}

### Dependencies and Libraries
[[LLM: Define key dependencies and libraries]]

<<REPEAT dependency>>
- **{{dependency_name}}** ({{dependency_version}})
  - Purpose: {{dependency_purpose}}
  - Justification: {{dependency_justification}}
  - Alternatives Considered: {{dependency_alternatives}}
<<END_REPEAT>>

## Data Architecture

### Data Model
[[LLM: Define the service's data model and entities]]

<<REPEAT entity>>
#### {{entity_name}}

**Purpose**: {{entity_purpose}}

**Attributes**:
<<REPEAT attribute>>
- **{{attribute_name}}** ({{attribute_type}}): {{attribute_description}}
  - Constraints: {{attribute_constraints}}
  - Default: {{attribute_default}}
<<END_REPEAT>>

**Relationships**:
- {{relationship_1}}: {{relationship_1_description}}
- {{relationship_2}}: {{relationship_2_description}}

**Business Rules**:
- {{business_rule_1}}
- {{business_rule_2}}
<<END_REPEAT>>

### Database Design
[[LLM: Define database technology and schema design]]

**Database Technology**: {{database_technology}}

**Database Justification**: {{database_justification}}

**Schema Design**:
- **Tables/Collections**: {{schema_tables}}
- **Indexes**: {{schema_indexes}}
- **Constraints**: {{schema_constraints}}
- **Partitioning**: {{schema_partitioning}}

**Data Access Patterns**:
- **Read Patterns**: {{read_patterns}}
- **Write Patterns**: {{write_patterns}}
- **Query Optimization**: {{query_optimization}}

### Data Storage Strategy
[[LLM: Define data storage and persistence strategy]]

**Storage Requirements**:
- **Data Volume**: {{data_volume_estimates}}
- **Growth Rate**: {{data_growth_rate}}
- **Retention Policy**: {{data_retention_policy}}
- **Backup Strategy**: {{backup_strategy}}

**Performance Requirements**:
- **Read Latency**: {{read_latency_target}}
- **Write Latency**: {{write_latency_target}}
- **Throughput**: {{throughput_target}}
- **Consistency**: {{consistency_requirements}}

## API Architecture

### API Design
[[LLM: Define the service's API architecture]]

**API Style**: {{api_style}} (REST / GraphQL / gRPC / Event-driven)

**API Version**: {{api_version}}

**Base URL Pattern**: {{base_url_pattern}}

**Authentication**: {{api_authentication}}

### Endpoint Architecture
[[LLM: Define API endpoint structure and patterns]]

**Resource Modeling**:
- **Primary Resources**: {{primary_resources}}
- **Sub-resources**: {{sub_resources}}
- **Resource Relationships**: {{resource_relationships}}

**URL Design Patterns**:
- **Collection URLs**: {{collection_url_pattern}}
- **Resource URLs**: {{resource_url_pattern}}
- **Action URLs**: {{action_url_pattern}}

### Request/Response Patterns
[[LLM: Define request and response handling patterns]]

**Request Handling**:
- **Validation Strategy**: {{request_validation}}
- **Parameter Processing**: {{parameter_processing}}
- **Content Negotiation**: {{content_negotiation}}
- **Rate Limiting**: {{rate_limiting_strategy}}

**Response Patterns**:
- **Success Responses**: {{success_response_patterns}}
- **Error Responses**: {{error_response_patterns}}
- **Pagination**: {{pagination_strategy}}
- **Caching Headers**: {{caching_headers}}

## Service Integration

### Upstream Dependencies
[[LLM: Define services this service depends on]]

<<REPEAT upstream_service>>
#### {{upstream_service_name}}

**Integration Type**: {{integration_type}} (Synchronous / Asynchronous / Event-driven)

**Interface**: {{integration_interface}}

**Communication Pattern**: {{communication_pattern}}

**Failure Handling**: {{failure_handling_strategy}}

**Circuit Breaker Configuration**:
- **Timeout**: {{circuit_breaker_timeout}}
- **Failure Threshold**: {{circuit_breaker_threshold}}
- **Recovery Time**: {{circuit_breaker_recovery}}

**Retry Strategy**:
- **Max Retries**: {{max_retries}}
- **Backoff Strategy**: {{backoff_strategy}}
- **Retry Conditions**: {{retry_conditions}}
<<END_REPEAT>>

### Downstream Consumers
[[LLM: Define services that consume this service]]

<<REPEAT downstream_service>>
#### {{downstream_service_name}}

**Consumption Pattern**: {{consumption_pattern}}

**SLA Provided**: {{sla_provided}}

**Rate Limits**: {{rate_limits}}

**Authentication Requirements**: {{auth_requirements}}

**Monitoring**: {{consumer_monitoring}}
<<END_REPEAT>>

### Event Integration
[[LLM: Define event publishing and consumption]]

**Events Published**:
<<REPEAT published_event>>
- **{{event_name}}**
  - Trigger: {{event_trigger}}
  - Schema: {{event_schema}}
  - Delivery Guarantee: {{delivery_guarantee}}
  - Ordering: {{event_ordering}}
<<END_REPEAT>>

**Events Consumed**:
<<REPEAT consumed_event>>
- **{{consumed_event_name}}**
  - Source: {{event_source}}
  - Handler: {{event_handler}}
  - Processing: {{event_processing}}
  - Error Handling: {{event_error_handling}}
<<END_REPEAT>>

## Application Architecture

### Service Structure
[[LLM: Define internal service architecture and layers]]

**Architectural Layers**:
- **Presentation Layer**: {{presentation_layer}}
- **Business Logic Layer**: {{business_logic_layer}}
- **Data Access Layer**: {{data_access_layer}}
- **Integration Layer**: {{integration_layer}}

**Design Patterns**:
- **Primary Patterns**: {{primary_design_patterns}}
- **Dependency Injection**: {{dependency_injection}}
- **Repository Pattern**: {{repository_pattern}}
- **Factory Pattern**: {{factory_pattern}}

### Code Organization
[[LLM: Define code structure and organization]]

**Package/Module Structure**:
```
{{code_structure}}
```

**Naming Conventions**:
- **Classes/Types**: {{class_naming}}
- **Methods/Functions**: {{method_naming}}
- **Variables**: {{variable_naming}}
- **Constants**: {{constant_naming}}

### Configuration Management
[[LLM: Define configuration strategy]]

**Configuration Sources**:
- **Environment Variables**: {{env_variables}}
- **Configuration Files**: {{config_files}}
- **External Config Service**: {{external_config}}
- **Runtime Configuration**: {{runtime_config}}

**Configuration Categories**:
- **Database Configuration**: {{db_config}}
- **Service Endpoints**: {{service_endpoints_config}}
- **Security Configuration**: {{security_config}}
- **Feature Flags**: {{feature_flags_config}}

## Security Architecture

### Authentication and Authorization
[[LLM: Define security implementation]]

**Authentication Implementation**:
- **Token Validation**: {{token_validation}}
- **Session Management**: {{session_management}}
- **Multi-factor Authentication**: {{mfa_implementation}}

**Authorization Implementation**:
- **Role-Based Access Control**: {{rbac_implementation}}
- **Resource-Level Permissions**: {{resource_permissions}}
- **Policy Enforcement**: {{policy_enforcement}}

### Data Security
[[LLM: Define data protection measures]]

**Encryption**:
- **Data at Rest**: {{encryption_at_rest}}
- **Data in Transit**: {{encryption_in_transit}}
- **Key Management**: {{key_management}}

**Data Protection**:
- **PII Handling**: {{pii_handling}}
- **Data Masking**: {{data_masking}}
- **Audit Logging**: {{audit_logging}}

## Performance Architecture

### Performance Optimization
[[LLM: Define performance optimization strategies]]

**Caching Strategy**:
- **Application Caching**: {{application_caching}}
- **Database Caching**: {{database_caching}}
- **External Caching**: {{external_caching}}
- **Cache Invalidation**: {{cache_invalidation}}

**Performance Patterns**:
- **Connection Pooling**: {{connection_pooling}}
- **Lazy Loading**: {{lazy_loading}}
- **Batch Processing**: {{batch_processing}}
- **Asynchronous Processing**: {{async_processing}}

### Scalability Design
[[LLM: Define scalability implementation]]

**Horizontal Scaling**:
- **Stateless Design**: {{stateless_design}}
- **Load Distribution**: {{load_distribution}}
- **Auto-scaling Triggers**: {{autoscaling_triggers}}

**Resource Optimization**:
- **Memory Management**: {{memory_management}}
- **CPU Optimization**: {{cpu_optimization}}
- **I/O Optimization**: {{io_optimization}}

## Operational Architecture

### Monitoring and Observability
[[LLM: Define monitoring implementation]]

**Metrics Collection**:
- **Application Metrics**: {{application_metrics}}
- **Business Metrics**: {{business_metrics}}
- **Infrastructure Metrics**: {{infrastructure_metrics}}

**Logging Strategy**:
- **Log Levels**: {{log_levels}}
- **Log Format**: {{log_format}}
- **Log Aggregation**: {{log_aggregation}}
- **Structured Logging**: {{structured_logging}}

**Distributed Tracing**:
- **Trace Implementation**: {{trace_implementation}}
- **Span Configuration**: {{span_configuration}}
- **Trace Sampling**: {{trace_sampling}}

### Health and Diagnostics
[[LLM: Define health monitoring]]

**Health Checks**:
- **Liveness Probe**: {{liveness_probe}}
- **Readiness Probe**: {{readiness_probe}}
- **Startup Probe**: {{startup_probe}}

**Diagnostics**:
- **Performance Profiling**: {{performance_profiling}}
- **Memory Diagnostics**: {{memory_diagnostics}}
- **Error Tracking**: {{error_tracking}}

## Deployment Architecture

### Containerization
[[LLM: Define container strategy]]

**Container Configuration**:
- **Base Image**: {{base_image}}
- **Image Size Optimization**: {{image_optimization}}
- **Security Scanning**: {{security_scanning}}

**Container Runtime**:
- **Resource Limits**: {{resource_limits}}
- **Environment Configuration**: {{env_configuration}}
- **Volume Mounts**: {{volume_mounts}}

### Deployment Strategy
[[LLM: Define deployment approach]]

**Deployment Pattern**: {{deployment_pattern}}

**Environment Strategy**:
- **Development**: {{dev_environment}}
- **Staging**: {{staging_environment}}
- **Production**: {{production_environment}}

**Release Strategy**:
- **Blue-Green Deployment**: {{blue_green_deployment}}
- **Canary Deployment**: {{canary_deployment}}
- **Feature Flags**: {{feature_flags_deployment}}

## Testing Architecture

### Testing Strategy
[[LLM: Define testing approach]]

**Unit Testing**:
- **Testing Framework**: {{unit_test_framework}}
- **Coverage Target**: {{coverage_target}}
- **Mock Strategy**: {{mock_strategy}}

**Integration Testing**:
- **API Testing**: {{api_testing}}
- **Database Testing**: {{database_testing}}
- **External Service Testing**: {{external_service_testing}}

**Performance Testing**:
- **Load Testing**: {{load_testing}}
- **Stress Testing**: {{stress_testing}}
- **Performance Benchmarks**: {{performance_benchmarks}}

---

@{example_service_architecture}
# Product Catalog Service - Technical Architecture

## Service Overview

### Service Identity
**Service Name**: Product Catalog Service

**Service Type**: Domain Service

**Bounded Context**: Product Management Domain

**Team Ownership**: Product Team

**Technology Stack**: Node.js, Express, PostgreSQL, Redis

## Technology Stack

### Runtime Environment
**Programming Language**: TypeScript/Node.js

**Framework**: Express.js with TypeScript

**Runtime Version**: Node.js 18 LTS

**Justification**: Team expertise, excellent ecosystem, good performance for I/O intensive operations

### Dependencies and Libraries
- **express** (4.18.x): Web framework for API endpoints
- **pg** (8.8.x): PostgreSQL client for database operations
- **redis** (4.5.x): Caching and session management
- **joi** (17.7.x): Request validation and schema definition

## Data Architecture

### Data Model

#### Product Entity
**Purpose**: Core product information and catalog data

**Attributes**:
- **id** (UUID): Unique product identifier
- **name** (String): Product display name
- **description** (Text): Detailed product description
- **price** (Decimal): Current product price
- **category_id** (UUID): Reference to product category
- **inventory_count** (Integer): Available inventory quantity
- **created_at** (Timestamp): Product creation time
- **updated_at** (Timestamp): Last modification time

**Business Rules**:
- Price must be greater than zero
- Inventory count cannot be negative
- Product name must be unique within category

### Database Design
**Database Technology**: PostgreSQL 14

**Database Justification**: ACID compliance for inventory management, excellent JSON support for flexible product attributes, strong consistency guarantees

**Schema Design**:
- **Tables**: products, categories, product_attributes, inventory_logs
- **Indexes**: B-tree on id, name; GIN on attributes JSONB column
- **Constraints**: Foreign key constraints, check constraints for business rules
- **Partitioning**: Monthly partitioning on inventory_logs table

## API Architecture

### API Design
**API Style**: RESTful HTTP API

**API Version**: v1

**Base URL Pattern**: /api/v1/products

**Authentication**: Bearer token (JWT)

### Endpoint Architecture
**Resource Modeling**:
- **Primary Resources**: /products, /categories
- **Sub-resources**: /products/{id}/inventory, /products/{id}/attributes
- **Resource Relationships**: Products belong to categories, have attributes and inventory

**URL Design Patterns**:
- **Collection URLs**: GET /api/v1/products
- **Resource URLs**: GET /api/v1/products/{id}
- **Action URLs**: POST /api/v1/products/{id}/reserve-inventory
@{/example_service_architecture}

[[LLM: Process all template markup and create a comprehensive service architecture document. Use the example as guidance but create content specific to the user's service. Ensure all technical details, patterns, and implementation approaches are clearly defined.]]
