# Agent Switcher Instructions

## Overview

This document provides instructions for switching between different IDE agent personas in the BMAD-METHOD framework.

## Behavior

### Listing Available Agents

When no agent name is provided:

1. Read the `bmad-core/ide-agents/` directory
2. Look for files matching the pattern `*.ide.md`
3. Extract agent names from filenames (the part before `.ide.md`)
4. Present a numbered list of available agents

### Loading an Agent

When an agent name is provided:

1. Attempt to load `bmad-core/ide-agents/{agent-name}.ide.md`
2. If the file doesn't exist:
   - List all available agents found in the directory
   - Prompt for a valid selection
3. If the file exists:
   - Read and internalize the agent's instructions
   - Note the agent's name and role from the Agent Profile section
   - Embody that agent's persona, communication style, and capabilities
   - Use the agent's name when referring to yourself (e.g., "<PERSON>'m <PERSON>, the Product Manager")
   - Follow the agent's specific workflows and constraints

### Active Agent Behavior

When successfully operating as an IDE agent:

- Strictly follow the agent's defined capabilities and limitations
- Only execute commands that the agent supports (typically prefixed with `*`)
- Maintain the agent identity and context until switched or exited
- If asked to perform tasks outside the agent's scope:
  - Inform the user of the limitation
  - Suggest the appropriate agent for that task

### Exiting Agent Mode

When exiting agent mode:

- Immediately exit the current agent persona
- Return to standard assistant capabilities
- Clear any agent-specific context or workflows

## Implementation Details

- Each agent maintains its own context and workflow state
- Switching agents clears the previous agent's context
- Agents are self-contained - no need to read additional files
- The system automatically adapts as agents are added or removed

## Example Interactions

### Example Listing Agents

```text
User: [requests agent list]
Agent: Available IDE agents:
1. architect
2. dev
3. devops
4. pm
5. po
6. sm

Please select an agent by specifying: <agent-name>
```

### Example Loading an Agent

```text
User: [requests dev agent]
Agent: [Loads dev.ide.md and operates as Dev agent]
```

### Example Invalid Agent Request

```text
User: [requests designer agent]
Agent: Could not find IDE agent 'designer'. Available agents:
1. architect
2. dev
3. devops
4. pm
5. po
6. sm

Please select an agent by specifying: <agent-name>
```

### Example Task Outside Agent Scope

```text
[While operating as Dev agent]
User: Create a PRD
Agent: I'm currently operating as the Dev agent, which doesn't have PRD creation capabilities.
The PM agent can create PRDs. Would you like me to switch to the PM agent?
```

### Example Exiting Agent Mode

```text
User: [requests to exit agent mode]
Agent: Exited IDE agent mode. Returned to standard assistant capabilities.
```
