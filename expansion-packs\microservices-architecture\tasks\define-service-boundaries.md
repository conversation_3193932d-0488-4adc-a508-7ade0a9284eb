# Define Service Boundaries Task

## Purpose

Apply domain-driven design principles to identify and define microservice boundaries for a distributed system following the Enhanced Microservices Architecture framework from `Architecture_stack/Microservices_Architecture.md`. This task helps decompose complex systems into cohesive, loosely coupled services with clear responsibilities and ownership using organic system thinking and value stream orientation.

## Instructions

### 1. Domain Analysis

#### Understand the Business Domain
[[LLM: Guide the user through understanding their business domain]]

- **Domain Exploration**: Ask the user to describe their business domain, core business processes, and key business entities
- **Stakeholder Identification**: Identify who the key domain experts and stakeholders are
- **Business Capabilities**: List the main business capabilities the system needs to support
- **Current Pain Points**: Understand existing system limitations and scaling challenges

#### Identify Bounded Contexts
[[LLM: Help identify bounded contexts using domain-driven design]]

- **Context Mapping**: Create a visual map of different business contexts
- **Language Analysis**: Identify where the same terms mean different things in different contexts
- **Data Ownership**: Determine which contexts own which data entities
- **Process Boundaries**: Identify where business processes naturally separate

### 2. Service Identification

#### Apply Service Decomposition Patterns
[[LLM: Guide through different decomposition strategies following Architecture_stack patterns]]

**Decomposition by Business Capability** (Primary approach from Architecture_stack):
- List core business capabilities aligned with value streams
- Group related capabilities together following bounded context principles
- Identify capabilities that can be independently developed and deployed
- Consider team structure and ownership following Conway's Law
- Apply organic system thinking for natural service boundaries

**Decomposition by Domain Model**:
- Identify core domain entities and their relationships
- Group entities that are tightly coupled
- Separate entities that have different lifecycles
- Consider data consistency requirements

**Decomposition by Use Case/User Journey**:
- Map user journeys and use cases
- Identify steps that can be handled independently
- Group related use cases that share data or logic
- Consider user experience and performance implications

#### Evaluate Service Candidates
[[LLM: Help evaluate potential service boundaries]]

For each potential service, assess:

**Cohesion Criteria**:
- Does the service have a single, well-defined responsibility?
- Are all components within the service related to the same business capability?
- Would changes to one part of the service likely require changes to other parts?

**Coupling Criteria**:
- Can the service operate independently of other services?
- Are there minimal dependencies on other services?
- Can the service's data model evolve independently?

**Team Ownership**:
- Can a single team (6-10 people) own and maintain this service?
- Does the service align with organizational structure?
- Are there clear accountability boundaries?

### 3. Data Ownership Analysis

#### Define Data Boundaries
[[LLM: Help define data ownership and boundaries]]

**Data Ownership Principles**:
- Each service should own its data completely
- No direct database access between services
- Data consistency within service boundaries
- Eventual consistency across service boundaries

**Shared Data Challenges**:
- Identify data that multiple services need
- Determine the authoritative source for each data entity
- Plan for data synchronization and consistency
- Consider read replicas and event-driven updates

#### Handle Cross-Service Data Needs
[[LLM: Address data sharing patterns]]

**Data Access Patterns**:
- API calls for real-time data needs
- Event streaming for data synchronization
- CQRS for read/write separation
- Saga patterns for distributed transactions

### 4. Communication Patterns

#### Define Service Interactions
[[LLM: Plan how services will communicate]]

**Synchronous Communication**:
- REST APIs for request/response patterns
- GraphQL for flexible data queries
- gRPC for high-performance internal communication

**Asynchronous Communication**:
- Event streaming for data changes
- Message queues for reliable processing
- Pub/sub for loose coupling

**Communication Guidelines**:
- Minimize synchronous calls between services
- Use events for data consistency
- Implement circuit breakers and timeouts
- Plan for service unavailability

### 5. Validation and Refinement

#### Apply Service Design Principles
[[LLM: Validate service boundaries against best practices]]

**Single Responsibility Principle**:
- Each service should have one reason to change
- Services should be focused on a specific business capability
- Avoid services that do "everything"

**Autonomous Service Principle**:
- Services should be independently deployable
- Services should have their own data storage
- Services should minimize runtime dependencies

**Business-Aligned Principle**:
- Services should align with business domains
- Service boundaries should match team boundaries
- Services should reflect organizational structure

#### Identify Potential Issues
[[LLM: Help identify and address common boundary issues]]

**Common Anti-Patterns to Avoid**:
- **Distributed Monolith**: Services that are too tightly coupled
- **Chatty Services**: Too many fine-grained service calls
- **Shared Database**: Multiple services accessing the same database
- **God Services**: Services that handle too many responsibilities

**Refinement Questions**:
- Are any services too large or too small?
- Are there circular dependencies between services?
- Do services have clear, stable interfaces?
- Can services evolve independently?

### 6. Documentation and Communication

#### Create Service Boundary Documentation
[[LLM: Guide creation of comprehensive documentation]]

**Service Catalog**:
- List all identified services
- Define each service's purpose and scope
- Document service dependencies
- Identify service owners

**Boundary Definitions**:
- What each service is responsible for
- What each service is NOT responsible for
- Data owned by each service
- APIs exposed by each service

**Migration Strategy** (if decomposing existing system):
- Order of service extraction
- Data migration approach
- Rollback plans
- Risk mitigation strategies

## Deliverables

### Service Boundary Analysis Document
Create a comprehensive document that includes:

1. **Domain Analysis Summary**
   - Business domain overview
   - Identified bounded contexts
   - Key business capabilities

2. **Service Definitions**
   - List of identified services
   - Purpose and scope of each service
   - Data ownership boundaries
   - Team ownership assignments

3. **Communication Patterns**
   - Service interaction patterns
   - API design principles
   - Event schemas and flows

4. **Implementation Roadmap**
   - Service development priority
   - Migration strategy (if applicable)
   - Risk assessment and mitigation

### Service Boundary Validation Checklist
Use the service-boundary-checklist to validate the defined boundaries against best practices.

## Common Patterns and Examples

### E-commerce Domain Example
- **User Service**: User accounts, authentication, profiles
- **Product Catalog Service**: Product information, inventory, search
- **Order Service**: Shopping cart, order processing, order history
- **Payment Service**: Payment processing, billing, refunds
- **Notification Service**: Email, SMS, push notifications
- **Recommendation Service**: Product recommendations, personalization

### Financial Services Example
- **Account Service**: Account management, balance tracking
- **Transaction Service**: Transaction processing, history
- **Payment Service**: Payment initiation, clearing, settlement
- **Risk Service**: Fraud detection, compliance checking
- **Notification Service**: Customer communications
- **Reporting Service**: Financial reporting, analytics

## Tips for Success

1. **Start with the Domain**: Always begin with understanding the business domain before thinking about technical boundaries
2. **Involve Domain Experts**: Include business stakeholders in boundary definition discussions
3. **Iterate and Refine**: Service boundaries will evolve as you learn more about the domain
4. **Consider Team Structure**: Align service boundaries with team capabilities and organizational structure
5. **Think Long-term**: Consider how the system and organization might evolve over time
6. **Validate with Use Cases**: Test service boundaries against real user scenarios and business processes
