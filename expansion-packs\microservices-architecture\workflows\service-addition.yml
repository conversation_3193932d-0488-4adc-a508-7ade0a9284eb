workflow:
  id: service-addition
  name: Add New Service to Existing Distributed System
  description: >-
    Workflow for adding new microservices to existing distributed systems.
    Includes integration analysis, API design, deployment planning, and system impact assessment.
  type: brownfield
  project_types:
    - service-expansion
    - feature-addition
    - system-enhancement
    - capability-extension

  # Main service addition sequence
  service_addition_sequence:
    - agent: analyst
      creates: service-addition-brief.md
      optional_steps:
        - stakeholder_interviews
        - requirements_analysis
        - impact_assessment
      notes: "Analyze the need for the new service, its business justification, and impact on existing system. SAVE OUTPUT: Copy service addition brief to your project's docs/ folder."

    - agent: microservices-architect
      creates: service-integration-analysis.md
      requires: service-addition-brief.md
      uses: define-service-boundaries
      notes: "Analyze how the new service fits into existing architecture, define boundaries, and identify integration points. SAVE OUTPUT: Copy integration analysis to your project's docs/ folder."

    - agent: api-designer
      creates: new-service-api-contracts.md
      requires: service-integration-analysis.md
      uses: design-api-contracts
      notes: "Design API contracts for the new service including endpoints, events, and integration with existing services. SAVE OUTPUT: Copy API contracts to your project's docs/api-contracts/ folder."

    - agent: microservices-architect
      creates: system-impact-assessment.md
      requires: 
        - service-integration-analysis.md
        - new-service-api-contracts.md
      notes: "Assess impact on existing services, identify required changes, and plan migration strategy. SAVE OUTPUT: Copy impact assessment to your project's docs/ folder."

    - agent: architect
      creates: new-service-architecture.md
      requires: new-service-api-contracts.md
      uses: service-architecture-tmpl
      notes: "Create detailed technical architecture for the new service including technology stack, data model, and deployment strategy. SAVE OUTPUT: Copy service architecture to your project's docs/services/ folder."

    - agent: api-designer
      updates: existing-service-contracts.md
      requires: system-impact-assessment.md
      condition: existing_services_need_updates
      notes: "Update API contracts for existing services that need modifications to integrate with the new service. SAVE OUTPUT: Update existing contracts in docs/api-contracts/ folder."

    - agent: microservices-architect
      creates: deployment-integration-plan.md
      requires: 
        - new-service-architecture.md
        - system-impact-assessment.md
      uses: create-deployment-strategy
      notes: "Create deployment plan including rollout strategy, testing approach, and rollback procedures. SAVE OUTPUT: Copy deployment plan to your project's docs/ folder."

    - agent: po
      validates: all_service_addition_artifacts
      uses: system-integration-checklist
      notes: "Validate all service addition documents for consistency, completeness, and minimal impact on existing system."

    - agent: various
      updates: flagged_documents
      condition: po_validation_issues
      notes: "If PO finds issues, return to relevant agent to fix and re-export updated documents to docs/ folder."

    - workflow_end:
      action: ready_for_implementation
      notes: "New service planning complete. Ready for implementation. Explain implementation order, testing strategy, and deployment coordination."

  flow_diagram: |
    ```mermaid
    graph TD
        A[Start: Service Addition Request] --> B[analyst: service-addition-brief.md]
        B --> C[microservices-architect: service-integration-analysis.md]
        C --> D[api-designer: new-service-api-contracts.md]
        D --> E[microservices-architect: system-impact-assessment.md]
        E --> F[architect: new-service-architecture.md]
        F --> G{Existing Services Need Updates?}
        G -->|Yes| H[api-designer: update existing contracts]
        G -->|No| I[microservices-architect: deployment-integration-plan.md]
        H --> I
        I --> J[po: validate all artifacts]
        J --> K{PO finds issues?}
        K -->|Yes| L[Return to relevant agent for fixes]
        K -->|No| M[Ready for Implementation]
        L --> J
        
        B -.-> B1[Optional: stakeholder interviews]
        B -.-> B2[Optional: impact assessment]
        C -.-> C1[Service boundary analysis]
        D -.-> D1[Contract-first API design]
        E -.-> E1[System impact evaluation]
        I -.-> I1[Deployment strategy planning]
        
        style M fill:#90EE90
        style C fill:#FFE4B5
        style D fill:#FFE4B5
        style E fill:#FFE4B5
        style F fill:#FFE4B5
        style I fill:#FFE4B5
    ```

  decision_guidance:
    use_service_addition_when:
      - Adding new business capability to existing distributed system
      - Extending system functionality without major architectural changes
      - Decomposing functionality from existing services
      - Adding specialized services (analytics, notifications, etc.)
      - Integrating with new external systems or partners

    consider_alternatives_when:
      - New functionality fits well within existing service boundaries
      - Change would require major modifications to multiple existing services
      - System architecture needs fundamental redesign
      - New capability is temporary or experimental

  integration_considerations:
    existing_system_analysis:
      - Review current service boundaries and responsibilities
      - Identify potential conflicts or overlaps with new service
      - Assess current system capacity and performance impact
      - Evaluate existing API contracts and versioning strategies

    backward_compatibility:
      - Ensure new service doesn't break existing functionality
      - Plan API versioning for any required changes to existing services
      - Design graceful degradation if new service is unavailable
      - Maintain existing SLAs and performance characteristics

    data_consistency:
      - Identify shared data and ownership boundaries
      - Plan data migration if moving functionality between services
      - Design event-driven synchronization for cross-service data
      - Ensure consistency patterns align with existing system

  handoff_prompts:
    analyst_to_architect: "Service addition brief complete. Save it as docs/service-addition-brief.md, then analyze integration with existing system architecture."
    integration_to_api: "Integration analysis ready. Save it as docs/service-integration-analysis.md, then design API contracts for the new service."
    api_to_impact: "New service API contracts complete. Save them in docs/api-contracts/, then assess impact on existing services."
    impact_to_service_arch: "Impact assessment ready. Save it as docs/system-impact-assessment.md, then create detailed architecture for the new service."
    service_arch_to_existing_apis: "New service architecture complete. Save it as docs/services/new-service-architecture.md. Do existing services need API updates?"
    existing_apis_to_deployment: "Existing service contracts updated. Save changes in docs/api-contracts/, then create deployment integration plan."
    deployment_to_po: "Deployment plan ready. Save it as docs/deployment-integration-plan.md. Please validate all service addition artifacts."
    po_issues: "PO found issues with [document]. Please return to [agent] to fix and re-save the updated document."
    addition_complete: "Service addition planning complete. All artifacts saved in docs/ folder. Ready for implementation and testing."

  risk_mitigation:
    common_risks:
      - Integration complexity with existing services
      - Performance impact on existing system
      - Data consistency challenges
      - Deployment coordination complexity
      - Team coordination and ownership clarity

    mitigation_strategies:
      - Comprehensive integration testing before deployment
      - Gradual rollout with feature flags and canary deployment
      - Clear API contracts and versioning strategy
      - Monitoring and alerting for system impact
      - Clear team ownership and responsibility boundaries

  success_criteria:
    planning_success:
      - New service boundaries are clearly defined and non-overlapping
      - Integration points are well-designed and documented
      - Impact on existing services is minimal and well-understood
      - Deployment strategy minimizes risk and downtime
      - Team ownership and responsibilities are clear

    implementation_success:
      - New service integrates seamlessly with existing system
      - No degradation in existing service performance or reliability
      - API contracts are properly versioned and backward compatible
      - Monitoring and observability cover new service and integrations
      - Documentation is complete and accessible to all teams
