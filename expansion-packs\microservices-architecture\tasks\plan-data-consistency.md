# Plan Data Consistency Task

## Purpose

Design data consistency patterns and strategies for distributed microservices systems. This task helps establish appropriate consistency models, transaction boundaries, and data synchronization patterns that balance consistency requirements with system performance and availability.

## Instructions

### 1. Understand Consistency Requirements

#### Analyze Business Requirements
[[LLM: Guide the user through understanding their consistency needs]]

- **Business Processes**: Identify critical business processes and their consistency requirements
- **Data Relationships**: Map relationships between data entities across services
- **Consistency Criticality**: Determine which data must be immediately consistent vs. eventually consistent
- **Business Impact**: Understand the business impact of temporary inconsistency

#### Identify Consistency Levels
[[LLM: Help categorize different consistency requirements]]

**Strong Consistency Requirements**:
- Financial transactions and account balances
- Inventory reservations and stock levels
- User authentication and authorization data
- Critical business rules and constraints

**Eventual Consistency Acceptable**:
- User profile updates and preferences
- Product catalog information
- Analytics and reporting data
- Notification and communication logs

**Weak Consistency Acceptable**:
- User activity logs and metrics
- Search indexes and caches
- Recommendation data
- Non-critical metadata

### 2. Define Transaction Boundaries

#### Identify ACID Transaction Boundaries
[[LLM: Help define where ACID transactions are needed]]

**Single Service Transactions**:
- Operations that must be atomic within a service boundary
- Data that must be immediately consistent
- Operations that cannot tolerate partial failures
- Critical business invariants that must be maintained

**Transaction Scope Guidelines**:
- Keep transactions within service boundaries
- Minimize transaction duration
- Avoid distributed transactions where possible
- Use compensating actions for cross-service operations

#### Design Distributed Transaction Patterns
[[LLM: Plan patterns for cross-service transactions]]

**Saga Pattern Implementation**:
- Choreography vs. Orchestration approaches
- Compensation logic for each step
- Timeout and retry strategies
- Failure handling and rollback procedures

**Two-Phase Commit Considerations**:
- When 2PC is absolutely necessary
- Performance and availability implications
- Coordinator service design
- Participant service requirements

### 3. Event-Driven Consistency Patterns

#### Design Event Sourcing Strategy
[[LLM: Plan event sourcing for data consistency]]

**Event Store Design**:
- Event schema and versioning
- Event ordering and partitioning
- Event replay and recovery mechanisms
- Snapshot strategies for performance

**Event Processing Patterns**:
- Event handlers and projections
- Idempotent event processing
- Event deduplication strategies
- Error handling and dead letter queues

#### Implement Event-Driven Synchronization
[[LLM: Design event-driven data synchronization]]

**Event Publishing Patterns**:
- Domain events vs. integration events
- Event enrichment strategies
- Event filtering and routing
- Event ordering guarantees

**Event Consumption Patterns**:
- At-least-once vs. exactly-once processing
- Consumer group management
- Offset management and checkpointing
- Consumer scaling and load balancing

### 4. Data Replication Strategies

#### Design Read Replicas
[[LLM: Plan read replica strategies]]

**Replica Types**:
- Synchronous vs. asynchronous replication
- Read-only replicas for query optimization
- Geographic replicas for global distribution
- Materialized views for specific use cases

**Replica Consistency**:
- Replication lag tolerance
- Read-your-writes consistency
- Monotonic read consistency
- Causal consistency requirements

#### Implement CQRS Patterns
[[LLM: Design Command Query Responsibility Segregation]]

**Command Side Design**:
- Write model optimization
- Command validation and processing
- Event generation and publishing
- Write-side scaling strategies

**Query Side Design**:
- Read model optimization
- Projection building and maintenance
- Query-specific data structures
- Read-side scaling strategies

### 5. Conflict Resolution Strategies

#### Design Conflict Detection
[[LLM: Plan conflict detection mechanisms]]

**Optimistic Concurrency Control**:
- Version-based conflict detection
- Timestamp-based approaches
- Hash-based change detection
- Application-level conflict detection

**Conflict Scenarios**:
- Concurrent updates to shared data
- Network partition scenarios
- Service unavailability situations
- Race conditions in distributed operations

#### Implement Conflict Resolution
[[LLM: Design conflict resolution strategies]]

**Resolution Strategies**:
- Last-writer-wins (LWW)
- First-writer-wins (FWW)
- Application-specific merge logic
- User-driven conflict resolution

**Business Rule Application**:
- Priority-based resolution
- Business context consideration
- Stakeholder notification
- Audit trail maintenance

### 6. Consistency Monitoring and Validation

#### Design Consistency Checks
[[LLM: Plan consistency monitoring]]

**Automated Validation**:
- Cross-service data validation
- Consistency check scheduling
- Anomaly detection algorithms
- Reconciliation processes

**Monitoring Metrics**:
- Replication lag measurements
- Event processing delays
- Consistency violation detection
- Data drift monitoring

#### Implement Reconciliation Processes
[[LLM: Design data reconciliation]]

**Reconciliation Strategies**:
- Periodic full reconciliation
- Incremental reconciliation
- Event-driven reconciliation
- On-demand reconciliation

**Reconciliation Actions**:
- Data correction procedures
- Notification and alerting
- Manual intervention processes
- Audit and compliance reporting

### 7. Performance Optimization

#### Optimize Consistency Patterns
[[LLM: Balance consistency with performance]]

**Performance Considerations**:
- Consistency level vs. performance trade-offs
- Network latency impact
- Storage performance implications
- CPU and memory overhead

**Optimization Strategies**:
- Batching and bulk operations
- Asynchronous processing where possible
- Caching strategies for consistent data
- Connection pooling and reuse

#### Design Caching Strategies
[[LLM: Plan caching with consistency]]

**Cache Consistency Patterns**:
- Write-through caching
- Write-behind caching
- Cache invalidation strategies
- Distributed cache coordination

**Cache Levels**:
- Application-level caching
- Database query caching
- CDN and edge caching
- Browser and client caching

### 8. Failure Handling and Recovery

#### Design Failure Scenarios
[[LLM: Plan for consistency during failures]]

**Failure Types**:
- Network partitions and connectivity issues
- Service unavailability and downtime
- Database failures and corruption
- Message broker failures

**Failure Impact Assessment**:
- Data consistency implications
- Business process impact
- Recovery time requirements
- Data loss tolerance

#### Implement Recovery Procedures
[[LLM: Design recovery mechanisms]]

**Recovery Strategies**:
- Automatic recovery procedures
- Manual intervention processes
- Data restoration from backups
- Event replay and reconstruction

**Recovery Validation**:
- Data integrity verification
- Consistency validation
- Business rule verification
- Performance impact assessment

## Deliverables

### Data Consistency Plan Document
Create a comprehensive document that includes:

1. **Consistency Requirements Analysis**
   - Business process consistency needs
   - Data relationship mapping
   - Consistency level categorization
   - Trade-off analysis

2. **Transaction Design**
   - ACID transaction boundaries
   - Distributed transaction patterns
   - Saga implementation plans
   - Compensation strategies

3. **Event-Driven Patterns**
   - Event sourcing strategy
   - Event synchronization patterns
   - Event processing guarantees
   - Error handling procedures

4. **Monitoring and Validation**
   - Consistency monitoring strategy
   - Reconciliation procedures
   - Performance optimization
   - Failure recovery plans

### Implementation Guidelines
Provide specific guidance for:

- Development team responsibilities
- Testing strategies for consistency
- Deployment considerations
- Operational procedures

## Common Patterns and Examples

### E-commerce Consistency Patterns

**Strong Consistency**:
- Order placement and payment processing
- Inventory reservation and allocation
- User account balance updates

**Eventual Consistency**:
- Product catalog updates
- User preference synchronization
- Order history and analytics

**Consistency Implementation**:
- Saga pattern for order processing
- Event sourcing for inventory management
- CQRS for product catalog and search

### Financial Services Patterns

**Strong Consistency**:
- Account balance updates
- Transaction processing
- Regulatory compliance data

**Eventual Consistency**:
- Customer profile updates
- Marketing preferences
- Analytics and reporting

**Consistency Implementation**:
- Two-phase commit for critical transactions
- Event sourcing for audit trails
- Read replicas for reporting

## Best Practices

1. **Start with Business Requirements**: Always begin with understanding business consistency needs
2. **Prefer Eventual Consistency**: Use strong consistency only when absolutely necessary
3. **Design for Failure**: Plan for network partitions and service failures
4. **Monitor Consistency**: Implement comprehensive monitoring and alerting
5. **Test Consistency Scenarios**: Include consistency testing in your test strategy
6. **Document Trade-offs**: Clearly document consistency decisions and trade-offs
7. **Plan for Evolution**: Design consistency patterns that can evolve with the system

## Anti-Patterns to Avoid

1. **Distributed Transactions Everywhere**: Avoid using distributed transactions as the default pattern
2. **Ignoring CAP Theorem**: Don't try to achieve perfect consistency, availability, and partition tolerance
3. **Synchronous Everything**: Avoid making all operations synchronous for consistency
4. **No Monitoring**: Don't deploy without consistency monitoring and validation
5. **Complex Compensation**: Avoid overly complex compensation logic in sagas
6. **Tight Coupling**: Don't create tight coupling between services for consistency
