# {{system_name}} - System Integration Plan

[[LLM: This template creates a comprehensive integration plan for distributed systems. Focus on service communication, data flow, and system-wide coordination patterns. Use advanced elicitation to gather integration requirements.]]

## Integration Overview

### System Integration Strategy
[[LLM: Define the overall integration approach]]

**Integration Philosophy**: {{integration_philosophy}}

**Communication Patterns**: {{communication_patterns}}

**Data Flow Strategy**: {{data_flow_strategy}}

**Consistency Model**: {{consistency_model}}

### Integration Scope
[[LLM: Define what is covered by this integration plan]]

**Services in Scope**: {{services_in_scope}}

**External Systems**: {{external_systems}}

**Integration Points**: {{integration_points}}

**Excluded from Scope**: {{excluded_scope}}

## Service Communication Architecture

### Synchronous Communication
[[LLM: Define synchronous communication patterns]]

**REST API Communication**:
- **API Gateway**: {{api_gateway_strategy}}
- **Service Discovery**: {{service_discovery_method}}
- **Load Balancing**: {{load_balancing_strategy}}
- **Circuit Breakers**: {{circuit_breaker_configuration}}

**Communication Matrix**:
<<REPEAT sync_communication>>
- **{{source_service}}** → **{{target_service}}**
  - Protocol: {{communication_protocol}}
  - Purpose: {{communication_purpose}}
  - SLA: {{communication_sla}}
  - Fallback: {{fallback_strategy}}
<<END_REPEAT>>

### Asynchronous Communication
[[LLM: Define asynchronous communication patterns]]

**Message Broker Architecture**:
- **Broker Technology**: {{message_broker}}
- **Topic/Queue Strategy**: {{topic_queue_strategy}}
- **Message Ordering**: {{message_ordering}}
- **Delivery Guarantees**: {{delivery_guarantees}}

**Event Flow Patterns**:
<<REPEAT event_flow>>
- **Event**: {{event_name}}
  - Publisher: {{event_publisher}}
  - Consumers: {{event_consumers}}
  - Schema: {{event_schema}}
  - Routing: {{event_routing}}
<<END_REPEAT>>

### Service Mesh Integration
[[LLM: Define service mesh configuration]]

^^CONDITION: uses_service_mesh^^
**Service Mesh Configuration**:
- **Technology**: {{service_mesh_technology}}
- **Traffic Management**: {{traffic_management}}
- **Security Policies**: {{security_policies}}
- **Observability**: {{observability_configuration}}

**Service Mesh Features**:
- **mTLS**: {{mtls_configuration}}
- **Traffic Splitting**: {{traffic_splitting}}
- **Circuit Breaking**: {{circuit_breaking}}
- **Retry Policies**: {{retry_policies}}
^^END_CONDITION^^

## Data Integration Strategy

### Data Ownership and Boundaries
[[LLM: Define data ownership across services]]

**Data Ownership Matrix**:
<<REPEAT data_ownership>>
- **{{data_entity}}**
  - Owner Service: {{owner_service}}
  - Access Pattern: {{access_pattern}}
  - Consistency Level: {{consistency_level}}
  - Sharing Strategy: {{sharing_strategy}}
<<END_REPEAT>>

### Data Synchronization Patterns
[[LLM: Define how data is synchronized across services]]

**Event-Driven Synchronization**:
- **Change Data Capture**: {{cdc_strategy}}
- **Event Sourcing**: {{event_sourcing_usage}}
- **Saga Patterns**: {{saga_implementation}}
- **Compensation Logic**: {{compensation_strategy}}

**Data Replication**:
- **Read Replicas**: {{read_replica_strategy}}
- **CQRS Implementation**: {{cqrs_implementation}}
- **Materialized Views**: {{materialized_views}}
- **Cache Synchronization**: {{cache_sync_strategy}}

### Cross-Service Transactions
[[LLM: Define distributed transaction patterns]]

**Transaction Patterns**:
- **Two-Phase Commit**: {{two_phase_commit_usage}}
- **Saga Pattern**: {{saga_pattern_implementation}}
- **Eventual Consistency**: {{eventual_consistency_strategy}}
- **Compensation Actions**: {{compensation_actions}}

**Transaction Coordination**:
- **Orchestration vs Choreography**: {{coordination_approach}}
- **Transaction Timeout**: {{transaction_timeout}}
- **Failure Handling**: {{failure_handling}}
- **Rollback Strategy**: {{rollback_strategy}}

## External System Integration

### Third-Party Service Integration
[[LLM: Define integration with external services]]

<<REPEAT external_integration>>
#### {{external_system_name}}

**Integration Type**: {{integration_type}}

**Communication Protocol**: {{external_protocol}}

**Authentication**: {{external_authentication}}

**Data Exchange**:
- **Input Format**: {{input_format}}
- **Output Format**: {{output_format}}
- **Data Mapping**: {{data_mapping}}
- **Validation Rules**: {{validation_rules}}

**Error Handling**:
- **Retry Strategy**: {{external_retry_strategy}}
- **Circuit Breaker**: {{external_circuit_breaker}}
- **Fallback Mechanism**: {{external_fallback}}
- **Error Logging**: {{external_error_logging}}

**SLA and Monitoring**:
- **Response Time SLA**: {{external_sla}}
- **Availability Requirements**: {{external_availability}}
- **Monitoring Strategy**: {{external_monitoring}}
- **Alerting**: {{external_alerting}}
<<END_REPEAT>>

### Legacy System Integration
[[LLM: Define integration with legacy systems]]

^^CONDITION: has_legacy_systems^^
**Legacy Integration Strategy**:
- **Integration Approach**: {{legacy_integration_approach}}
- **Data Migration**: {{legacy_data_migration}}
- **Protocol Adaptation**: {{legacy_protocol_adaptation}}
- **Modernization Plan**: {{legacy_modernization}}

**Legacy System Details**:
<<REPEAT legacy_system>>
- **{{legacy_system_name}}**
  - Technology: {{legacy_technology}}
  - Integration Method: {{legacy_integration_method}}
  - Data Format: {{legacy_data_format}}
  - Migration Timeline: {{legacy_migration_timeline}}
<<END_REPEAT>>
^^END_CONDITION^^

## Security Integration

### Authentication and Authorization
[[LLM: Define security integration across services]]

**Authentication Strategy**:
- **Identity Provider**: {{identity_provider}}
- **Token Format**: {{token_format}}
- **Token Validation**: {{token_validation}}
- **Session Management**: {{session_management}}

**Authorization Model**:
- **RBAC Implementation**: {{rbac_implementation}}
- **Policy Enforcement**: {{policy_enforcement}}
- **Resource Protection**: {{resource_protection}}
- **Audit Logging**: {{audit_logging}}

### Service-to-Service Security
[[LLM: Define inter-service security]]

**Service Authentication**:
- **mTLS Configuration**: {{mtls_configuration}}
- **Service Certificates**: {{service_certificates}}
- **Certificate Rotation**: {{certificate_rotation}}
- **Trust Boundaries**: {{trust_boundaries}}

**API Security**:
- **API Gateway Security**: {{api_gateway_security}}
- **Rate Limiting**: {{rate_limiting}}
- **Input Validation**: {{input_validation}}
- **Output Filtering**: {{output_filtering}}

## Monitoring and Observability Integration

### Distributed Tracing
[[LLM: Define tracing across services]]

**Tracing Strategy**:
- **Tracing Technology**: {{tracing_technology}}
- **Trace Propagation**: {{trace_propagation}}
- **Sampling Strategy**: {{sampling_strategy}}
- **Trace Storage**: {{trace_storage}}

**Trace Correlation**:
- **Correlation IDs**: {{correlation_ids}}
- **Span Relationships**: {{span_relationships}}
- **Cross-Service Tracing**: {{cross_service_tracing}}
- **Error Correlation**: {{error_correlation}}

### Metrics and Alerting
[[LLM: Define metrics collection and alerting]]

**Metrics Collection**:
- **Metrics Technology**: {{metrics_technology}}
- **Service Metrics**: {{service_metrics}}
- **Business Metrics**: {{business_metrics}}
- **Infrastructure Metrics**: {{infrastructure_metrics}}

**Alerting Strategy**:
- **Alert Definitions**: {{alert_definitions}}
- **Alert Routing**: {{alert_routing}}
- **Escalation Procedures**: {{escalation_procedures}}
- **Alert Correlation**: {{alert_correlation}}

### Logging Integration
[[LLM: Define logging across services]]

**Centralized Logging**:
- **Log Aggregation**: {{log_aggregation}}
- **Log Format**: {{log_format}}
- **Log Correlation**: {{log_correlation}}
- **Log Retention**: {{log_retention}}

**Log Analysis**:
- **Search and Query**: {{log_search}}
- **Log-based Alerting**: {{log_alerting}}
- **Log Analytics**: {{log_analytics}}
- **Compliance Logging**: {{compliance_logging}}

## Performance and Scalability Integration

### Performance Optimization
[[LLM: Define performance optimization across services]]

**Caching Strategy**:
- **Distributed Caching**: {{distributed_caching}}
- **Cache Invalidation**: {{cache_invalidation}}
- **Cache Consistency**: {{cache_consistency}}
- **Cache Monitoring**: {{cache_monitoring}}

**Connection Management**:
- **Connection Pooling**: {{connection_pooling}}
- **Keep-Alive Configuration**: {{keep_alive_config}}
- **Timeout Configuration**: {{timeout_configuration}}
- **Resource Limits**: {{resource_limits}}

### Scaling Coordination
[[LLM: Define scaling across services]]

**Auto-Scaling Strategy**:
- **Scaling Triggers**: {{scaling_triggers}}
- **Scaling Policies**: {{scaling_policies}}
- **Cross-Service Dependencies**: {{scaling_dependencies}}
- **Scaling Coordination**: {{scaling_coordination}}

**Load Distribution**:
- **Load Balancing**: {{load_balancing}}
- **Traffic Routing**: {{traffic_routing}}
- **Geographic Distribution**: {{geographic_distribution}}
- **Failover Strategy**: {{failover_strategy}}

## Deployment and Release Integration

### Deployment Coordination
[[LLM: Define deployment coordination across services]]

**Deployment Strategy**:
- **Deployment Order**: {{deployment_order}}
- **Dependency Management**: {{dependency_management}}
- **Rollback Coordination**: {{rollback_coordination}}
- **Feature Flag Integration**: {{feature_flag_integration}}

**Release Management**:
- **Release Planning**: {{release_planning}}
- **Version Compatibility**: {{version_compatibility}}
- **Migration Coordination**: {{migration_coordination}}
- **Communication Strategy**: {{communication_strategy}}

### Environment Coordination
[[LLM: Define environment management]]

**Environment Parity**:
- **Configuration Consistency**: {{config_consistency}}
- **Data Synchronization**: {{data_synchronization}}
- **Service Versions**: {{service_versions}}
- **Integration Testing**: {{integration_testing}}

## Testing Integration

### Integration Testing Strategy
[[LLM: Define integration testing approach]]

**Test Types**:
- **Contract Testing**: {{contract_testing}}
- **End-to-End Testing**: {{e2e_testing}}
- **Performance Testing**: {{performance_testing}}
- **Chaos Testing**: {{chaos_testing}}

**Test Environment**:
- **Test Data Management**: {{test_data_management}}
- **Service Mocking**: {{service_mocking}}
- **Test Isolation**: {{test_isolation}}
- **Test Automation**: {{test_automation}}

## Risk Management and Mitigation

### Integration Risks
[[LLM: Identify and assess integration risks]]

<<REPEAT integration_risk>>
**Risk**: {{risk_description}}
- **Impact**: {{risk_impact}}
- **Probability**: {{risk_probability}}
- **Mitigation**: {{risk_mitigation}}
- **Contingency**: {{risk_contingency}}
- **Monitoring**: {{risk_monitoring}}
<<END_REPEAT>>

### Failure Scenarios
[[LLM: Define failure handling]]

**Common Failure Scenarios**:
- **Service Unavailability**: {{service_unavailability_handling}}
- **Network Partitions**: {{network_partition_handling}}
- **Data Inconsistency**: {{data_inconsistency_handling}}
- **Performance Degradation**: {{performance_degradation_handling}}

## Implementation Roadmap

### Integration Phases
[[LLM: Define implementation phases]]

**Phase 1: {{phase_1_name}}**
- Duration: {{phase_1_duration}}
- Scope: {{phase_1_scope}}
- Deliverables: {{phase_1_deliverables}}
- Success Criteria: {{phase_1_success}}

**Phase 2: {{phase_2_name}}**
- Duration: {{phase_2_duration}}
- Scope: {{phase_2_scope}}
- Deliverables: {{phase_2_deliverables}}
- Success Criteria: {{phase_2_success}}

**Phase 3: {{phase_3_name}}**
- Duration: {{phase_3_duration}}
- Scope: {{phase_3_scope}}
- Deliverables: {{phase_3_deliverables}}
- Success Criteria: {{phase_3_success}}

---

@{example_integration_plan}
# E-commerce Platform - System Integration Plan

## Integration Overview

### System Integration Strategy
**Integration Philosophy**: Event-driven architecture with synchronous APIs for real-time operations

**Communication Patterns**: REST APIs for queries, event streaming for state changes, GraphQL for mobile clients

**Data Flow Strategy**: Domain events for cross-service communication, CQRS for read/write separation

**Consistency Model**: Strong consistency within services, eventual consistency across services

## Service Communication Architecture

### Synchronous Communication
**REST API Communication**:
- **API Gateway**: Kong API Gateway with rate limiting and authentication
- **Service Discovery**: Kubernetes DNS with Consul for advanced features
- **Load Balancing**: Kubernetes Service with session affinity
- **Circuit Breakers**: Hystrix with 5-second timeout, 50% failure threshold

**Communication Matrix**:
- **Order Service** → **Payment Service**
  - Protocol: HTTPS REST
  - Purpose: Process payment for orders
  - SLA: 500ms response time, 99.9% availability
  - Fallback: Queue payment for later processing

- **Order Service** → **Inventory Service**
  - Protocol: HTTPS REST
  - Purpose: Reserve inventory for orders
  - SLA: 200ms response time, 99.95% availability
  - Fallback: Allow overselling with manual reconciliation

### Asynchronous Communication
**Message Broker Architecture**:
- **Broker Technology**: Apache Kafka with 3 brokers, replication factor 3
- **Topic Strategy**: Domain-based topics (orders, payments, inventory, notifications)
- **Message Ordering**: Partition by customer ID for order-related events
- **Delivery Guarantees**: At-least-once delivery with idempotent consumers

**Event Flow Patterns**:
- **OrderCreated**
  - Publisher: Order Service
  - Consumers: Payment Service, Inventory Service, Notification Service
  - Schema: Order ID, customer ID, items, total amount
  - Routing: Broadcast to all consumers

- **PaymentProcessed**
  - Publisher: Payment Service
  - Consumers: Order Service, Notification Service, Analytics Service
  - Schema: Payment ID, order ID, amount, status
  - Routing: Broadcast to all consumers

## Data Integration Strategy

### Data Ownership Matrix
- **Customer Data**
  - Owner Service: Customer Service
  - Access Pattern: API calls for real-time data, events for updates
  - Consistency Level: Strong within service, eventual across services
  - Sharing Strategy: Customer events published on state changes

- **Product Catalog**
  - Owner Service: Product Catalog Service
  - Access Pattern: Read replicas for high-volume queries
  - Consistency Level: Eventual consistency acceptable
  - Sharing Strategy: Product update events, cached read replicas

- **Order Data**
  - Owner Service: Order Service
  - Access Pattern: API calls for order status, events for state changes
  - Consistency Level: Strong consistency required
  - Sharing Strategy: Order lifecycle events published

### Cross-Service Transactions
**Transaction Patterns**:
- **Saga Pattern**: Order processing workflow with compensation
- **Orchestration**: Order Service orchestrates payment and inventory
- **Compensation Actions**: Cancel payment, release inventory on failure
- **Timeout**: 30-second timeout for order processing saga
@{/example_integration_plan}

[[LLM: Process all template markup and create a comprehensive system integration plan. Use the example as guidance but create content specific to the user's distributed system. Ensure all integration patterns, communication flows, and coordination strategies are clearly defined.]]
