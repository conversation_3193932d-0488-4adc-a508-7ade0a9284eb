workflow:
  id: microservices-greenfield
  name: Greenfield Microservices System Development
  description: >-
    Comprehensive workflow for designing and planning distributed microservices systems from scratch.
    Includes system-level architecture, service boundary definition, API design, and microfrontend coordination.
  type: greenfield
  project_types:
    - distributed-web-app
    - microservices-platform
    - event-driven-system
    - scalable-saas
    - enterprise-distributed-app

  # For Complex Distributed Systems (Production-Ready, Multiple Services)
  complex_system_sequence:
    - agent: analyst
      creates: distributed-system-brief.md
      optional_steps:
        - brainstorming_session
        - market_research_prompt
        - stakeholder_interviews
      notes: "Create comprehensive system-level brief focusing on distributed system requirements, scale needs, and team organization. SAVE OUTPUT: Copy final distributed-system-brief.md to your project's docs/ folder."

    - agent: pm
      creates: distributed-prd.md
      requires: distributed-system-brief.md
      notes: "Create system-wide PRD with service interaction stories and cross-cutting requirements. SAVE OUTPUT: Copy final distributed-prd.md to your project's docs/ folder."

    - agent: microservices-architect
      creates: service-boundaries-analysis.md
      requires: distributed-prd.md
      uses: define-service-boundaries
      notes: "Analyze domain boundaries and define initial service decomposition using domain-driven design. SAVE OUTPUT: Copy service boundaries analysis to your project's docs/ folder."

    - agent: microservices-architect
      creates: system-integration-plan.md
      requires: service-boundaries-analysis.md
      uses: system-integration-tmpl
      notes: "Create comprehensive system integration strategy including communication patterns, data consistency, and deployment coordination. SAVE OUTPUT: Copy system integration plan to your project's docs/ folder."

    - agent: api-designer
      creates: api-contracts/
      requires: 
        - service-boundaries-analysis.md
        - system-integration-plan.md
      uses: design-api-contracts
      notes: "Design API contracts for each identified service including REST endpoints, event schemas, and service communication patterns. Create separate contract files for each service. SAVE OUTPUT: Copy all API contracts to your project's docs/api-contracts/ folder."

    - agent: microfrontend-architect
      creates: microfrontend-spec.md
      requires: distributed-prd.md
      condition: has_frontend_requirements
      uses: design-microfrontend-architecture
      notes: "OPTIONAL: Design microfrontend architecture if system includes frontend components. Define module boundaries, integration patterns, and shared resources. SAVE OUTPUT: Copy microfrontend spec to your project's docs/ folder."

    - agent: microservices-architect
      creates: deployment-strategy.md
      requires: 
        - system-integration-plan.md
        - api-contracts/
      uses: create-deployment-strategy
      notes: "Create comprehensive deployment strategy including container orchestration, service mesh, monitoring, and operational procedures. SAVE OUTPUT: Copy deployment strategy to your project's docs/ folder."

    - service_planning_phase:
      description: "Individual service detailed planning"
      for_each_service: true
      sequence:
        - agent: microservices-architect
          creates: services/{{service_name}}-brief.md
          uses: microservice-brief-tmpl
          notes: "Create detailed brief for each identified service including bounded context, responsibilities, and integration points."

        - agent: architect
          creates: services/{{service_name}}-architecture.md
          requires: services/{{service_name}}-brief.md
          uses: service-architecture-tmpl
          notes: "Create technical architecture for individual service including technology stack, data model, and implementation approach."

    - agent: po
      validates: all_artifacts
      uses: system-integration-checklist
      notes: "Validate all system-level documents for consistency, completeness, and feasibility. Check service boundaries, API contracts, and integration patterns."

    - agent: various
      updates: flagged_documents
      condition: po_validation_issues
      notes: "If PO finds issues, return to relevant agent to fix and re-export updated documents to docs/ folder."

    - workflow_end:
      action: move_to_implementation
      notes: "All distributed system planning artifacts complete. Ready for implementation phase. Explain to the user the next steps: service implementation order, team coordination, and development workflow setup."

  # For Simple Distributed Systems (Prototypes, MVPs, Limited Services)
  simple_system_sequence:
    - step: system_scope
      action: assess_complexity
      notes: "First, assess if this needs full distributed system planning (use complex_system_sequence) or can be a simple multi-service prototype."

    - agent: analyst
      creates: simple-system-brief.md
      optional_steps:
        - brainstorming_session
      notes: "Create focused system brief for simple distributed system. SAVE OUTPUT: Copy final simple-system-brief.md to your project's docs/ folder."

    - agent: microservices-architect
      creates: simple-service-plan.md
      requires: simple-system-brief.md
      uses: define-service-boundaries
      notes: "Define 2-4 core services with clear boundaries and simple integration patterns. Focus on MVP functionality."

    - agent: api-designer
      creates: simple-api-contracts.md
      requires: simple-service-plan.md
      notes: "Create basic API contracts for service communication. Focus on essential endpoints and simple data models."

    - workflow_end:
      action: move_to_implementation
      notes: "Simple distributed system defined. Ready for implementation. Explain service implementation order and basic deployment approach."

  flow_diagram: |
    ```mermaid
    graph TD
        A[Start: Microservices Project] --> B{System Complexity?}
        B -->|Complex/Production| C[analyst: distributed-system-brief.md]
        B -->|Simple/Prototype| D[analyst: simple-system-brief.md]
        
        C --> E[pm: distributed-prd.md]
        E --> F[microservices-architect: service-boundaries-analysis.md]
        F --> G[microservices-architect: system-integration-plan.md]
        G --> H[api-designer: api-contracts/]
        H --> I{Has Frontend?}
        I -->|Yes| J[microfrontend-architect: microfrontend-spec.md]
        I -->|No| K[microservices-architect: deployment-strategy.md]
        J --> K
        K --> L[Individual Service Planning]
        L --> M[po: validate all artifacts]
        M --> N{PO finds issues?}
        N -->|Yes| O[Return to relevant agent for fixes]
        N -->|No| P[Move to Implementation]
        O --> M
        
        D --> Q[microservices-architect: simple-service-plan.md]
        Q --> R[api-designer: simple-api-contracts.md]
        R --> S[Move to Implementation]
        
        C -.-> C1[Optional: brainstorming]
        C -.-> C2[Optional: market research]
        F -.-> F1[Domain-driven design analysis]
        H -.-> H1[Contract-first API design]
        J -.-> J1[Module federation planning]
        D -.-> D1[Optional: brainstorming]
        
        style P fill:#90EE90
        style S fill:#90EE90
        style F fill:#FFE4B5
        style G fill:#FFE4B5
        style H fill:#FFE4B5
        style J fill:#E6E6FA
        style K fill:#FFE4B5
        style Q fill:#FFB6C1
        style R fill:#FFB6C1
    ```

  decision_guidance:
    use_complex_sequence_when:
      - Building production-ready distributed systems
      - Multiple teams will own different services
      - Complex service interactions (5+ services)
      - Need comprehensive documentation and contracts
      - Long-term maintenance and evolution expected
      - Enterprise or high-scale applications
      - Regulatory compliance requirements

    use_simple_sequence_when:
      - Building prototypes or MVPs
      - Small team or single team ownership
      - Simple service interactions (2-4 services)
      - Quick experiments or proof-of-concepts
      - Short-term or learning projects
      - Clear service boundaries from start

  handoff_prompts:
    # Complex sequence prompts
    analyst_to_pm: "Distributed system brief is complete. Save it as docs/distributed-system-brief.md in your project, then create the system-wide PRD."
    pm_to_microservices_architect: "Distributed PRD is ready. Save it as docs/distributed-prd.md, then analyze service boundaries using domain-driven design."
    boundaries_to_integration: "Service boundaries defined. Save the analysis as docs/service-boundaries-analysis.md, then create the system integration plan."
    integration_to_api_designer: "System integration plan complete. Save it as docs/system-integration-plan.md, then design API contracts for each service."
    api_to_microfrontend: "API contracts ready. Save them in docs/api-contracts/ folder. Do you need microfrontend architecture for this system?"
    microfrontend_to_deployment: "Microfrontend spec complete. Save it as docs/microfrontend-spec.md, then create the deployment strategy."
    deployment_to_service_planning: "Deployment strategy ready. Save it as docs/deployment-strategy.md. Now let's plan each service individually."
    service_planning_to_po: "All service plans complete. Save them in docs/services/ folder. Please validate all artifacts for consistency."
    po_issues: "PO found issues with [document]. Please return to [agent] to fix and re-save the updated document."
    complex_complete: "All distributed system planning artifacts validated and saved in docs/ folder. Ready for implementation phase."

    # Simple sequence prompts
    simple_analyst_to_architect: "Simple system brief complete. Save it as docs/simple-system-brief.md, then define core services and boundaries."
    simple_architect_to_api: "Service plan ready. Save it as docs/simple-service-plan.md, then create basic API contracts."
    simple_complete: "Simple distributed system defined. Save API contracts as docs/simple-api-contracts.md. Ready for implementation."

  integration_with_existing_workflows:
    - Can be combined with infrastructure-devops expansion pack for deployment
    - Integrates with existing greenfield-fullstack for monolith-first approach
    - Can transition from brownfield-fullstack for monolith decomposition
    - Works with existing team configurations and agent orchestration
