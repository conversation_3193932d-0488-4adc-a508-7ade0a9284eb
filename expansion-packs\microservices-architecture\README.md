# Microservices Architecture Expansion Pack

## Overview

The Microservices Architecture expansion pack extends BMAD-METHOD v4.0.0+ with specialized agents, workflows, and templates for designing and implementing complex distributed web applications. This expansion pack is built upon and fully integrates with the comprehensive microservices architecture framework documented in `Architecture_stack/Microservices_Architecture.md`.

### Integration with Existing Architecture Documentation

This expansion pack follows the **Enhanced Microservices Architecture** framework established in the Architecture_stack documentation, including:

- **Organic System Thinking**: Treating systems as living organisms that learn, heal, and evolve
- **Value Stream Orientation**: Aligning services with business value streams and capabilities
- **Three Pillars of Communication**: Synchronous (REST, GraphQL, gRPC), Asynchronous (message queues), and Event Streaming (Kafka)
- **Conway's Law Considerations**: Aligning service boundaries with team structures
- **Evolutionary Architecture**: Designing systems that can adapt and evolve over time

The expansion pack enhances the existing documentation by providing:
- **Specialized AI Agents** that embody the architectural principles
- **Structured Workflows** that guide implementation of the patterns
- **Comprehensive Templates** that generate documentation following the established framework
- **Quality Checklists** that validate adherence to best practices

## Features

### Specialized Agents

#### 🏗️ Microservices Architect
- **Role**: System-level distributed architecture specialist
- **Expertise**: Service boundaries, data consistency patterns, distributed system design
- **Commands**: Service boundary analysis, system architecture design, technology stack recommendations
- **Focus**: Cross-service coordination, system-wide architectural decisions

#### 🔌 API Designer  
- **Role**: Service contract and API specification expert
- **Expertise**: RESTful API, GraphQL, event-driven architecture
- **Commands**: API contract creation, schema validation, service communication design
- **Focus**: Inter-service communication, API versioning, contract testing

#### 🎯 Microfrontend Architect
- **Role**: Frontend module coordination specialist  
- **Expertise**: Module federation, micro-app patterns, frontend service mesh
- **Commands**: Microfrontend specification, module boundary definition, integration planning
- **Focus**: Frontend modularity, shared dependencies, routing strategies

### Document Templates

- **distributed-system-brief-tmpl**: Overall system architecture and cross-service coordination
- **microservice-brief-tmpl**: Individual service specifications with bounded context
- **distributed-prd-tmpl**: System-wide product requirements with service interactions
- **service-prd-tmpl**: Service-specific product requirements and acceptance criteria
- **microfrontend-spec-tmpl**: Frontend module definitions with integration points
- **api-contract-tmpl**: Service-to-service communication specifications with versioning
- **service-architecture-tmpl**: Individual service technical architecture
- **system-integration-tmpl**: Cross-service integration and deployment strategy

### Workflows

#### Microservices Greenfield
Complete new distributed system development workflow:
1. System brief → Distributed PRD → Service identification → Individual service planning
2. Decision points: Monolith vs microservices, synchronous vs asynchronous communication
3. Handoff prompts between system-level and service-level planning

#### Service Addition
Adding new services to existing distributed systems:
- Integration with existing service mesh
- API gateway considerations
- Backward compatibility and versioning strategies

#### Microfrontend Integration
Frontend module coordination and deployment:
- Module federation setup
- Shared dependencies management
- Routing strategies

### Specialized Tasks

- **define-service-boundaries**: Domain-driven design for service identification
- **design-api-contracts**: RESTful and event-driven API specification
- **plan-data-consistency**: Eventual consistency and distributed transaction patterns
- **create-deployment-strategy**: Container orchestration and service mesh configuration
- **design-microfrontend-architecture**: Module federation and micro-app patterns

### Quality Checklists

- **service-boundary-checklist**: Validate service cohesion and coupling
- **api-contract-checklist**: Ensure proper versioning, documentation, and testing
- **microservice-readiness-checklist**: Deployment, monitoring, and operational readiness
- **system-integration-checklist**: Cross-service communication and data flow validation
- **microfrontend-integration-checklist**: Module loading, routing, and shared state management

## Installation

### Prerequisites
- BMAD-METHOD v4.0.0 or higher
- Node.js 14.0.0+

### Install via BMAD CLI
```bash
# Install the expansion pack
bmad install --expansion microservices-architecture

# Or install with specific team configuration
bmad install --expansion microservices-architecture --team microservices
```

### Manual Installation
1. Copy the `microservices-architecture/` directory to your `expansion-packs/` folder
2. Run the BMAD build process to integrate the new agents and templates
3. Verify installation with `bmad list --agents`

## Usage Examples

### Example 1: Planning a New E-commerce Microservices System

```bash
# Start with the microservices orchestrator
*agent microservices-architect

# Create system-level brief
*create-doc distributed-system-brief

# Generate distributed PRD
*agent pm
*create-doc distributed-prd

# Design service boundaries
*agent microservices-architect  
*task define-service-boundaries

# Design API contracts
*agent api-designer
*create-doc api-contract

# Plan microfrontend architecture
*agent microfrontend-architect
*create-doc microfrontend-spec
```

### Example 2: Adding Payment Service to Existing System

```bash
# Use service addition workflow
*workflow service-addition

# Analyze integration points
*agent api-designer
*task design-api-contracts

# Validate system integration
*checklist system-integration-checklist
```

## Team Configuration

### New Team: team-microservices
Specialized team for distributed system development including:
- BMad Orchestrator (coordination)
- Business Analyst (requirements)
- Product Manager (system-wide planning)
- Microservices Architect (system design)
- API Designer (service contracts)
- Microfrontend Architect (frontend modules)
- Solution Architect (technical foundation)
- Product Owner (story validation)
- Developer (implementation)
- QA Specialist (testing strategy)

## Integration with Existing BMAD Workflows

The microservices expansion pack seamlessly integrates with existing BMAD workflows:

- **Greenfield Projects**: Extends with distributed system planning following Architecture_stack patterns
- **Brownfield Projects**: Adds service decomposition and migration planning using established principles
- **Team Coordination**: Works with existing agent orchestration while respecting Conway's Law
- **Template System**: Uses BMAD template markup and processing with Architecture_stack references
- **Build System**: Integrates with existing dependency resolution

## Foundation: Architecture_stack Integration

This expansion pack is built upon the comprehensive microservices architecture framework in `Architecture_stack/Microservices_Architecture.md`. Key integrations include:

### Architectural Principles Alignment
- **Enhanced Microservices Architecture**: All agents and templates follow the established framework
- **Three Pillars of Communication**: Agents specialize in synchronous, asynchronous, and event streaming patterns
- **Organic System Thinking**: Workflows incorporate evolutionary and adaptive architecture principles
- **Value Stream Orientation**: Service boundaries align with business capabilities and value streams

### Technology Stack Consistency
- **Container Orchestration**: Kubernetes-first approach with 92% market adoption
- **Service Mesh**: Istio, Linkerd, and Consul Connect options based on requirements
- **Event Backbone**: Apache Kafka for event streaming with proper event sourcing patterns
- **Polyglot Persistence**: Right database for each service following data ownership principles

### Pattern Implementation
- **Domain-Driven Design**: Service boundary definition using DDD principles from Architecture_stack
- **CQRS and Event Sourcing**: Proper implementation of read/write separation and event-driven patterns
- **Circuit Breaker Patterns**: Resilience patterns with proper timeout and retry strategies
- **Zero-Trust Security**: mTLS, authentication, and authorization following security best practices

## Troubleshooting

### Common Issues

**Issue**: Agent transformation not working
**Solution**: Ensure expansion pack is properly installed and agents are available in `bmad list --agents`

**Issue**: Template processing errors
**Solution**: Verify template dependencies are resolved and follow BMAD template markup conventions

**Issue**: Workflow integration problems  
**Solution**: Check workflow dependencies and ensure core BMAD agents are available

### Debug Commands
```bash
# List available agents
bmad list --agents

# Validate expansion pack installation
bmad validate --expansion microservices-architecture

# Check agent dependencies
bmad status --agent microservices-architect
```

## Contributing

See the main BMAD-METHOD [CONTRIBUTING.md](../../CONTRIBUTING.md) for guidelines on contributing to expansion packs.

## License

This expansion pack is licensed under the same MIT license as the main BMAD-METHOD framework.

## Version History

- **v1.0.0**: Initial release with microservices and microfrontend architecture support
  - 3 specialized agents
  - 8 document templates  
  - 3 workflows
  - 5 specialized tasks
  - 5 quality checklists
