# {{application_name}} - Microfrontend Architecture Specification

[[LLM: This template creates a comprehensive specification for microfrontend architecture. Focus on module boundaries, integration patterns, and team coordination. Use advanced elicitation to gather frontend requirements.]]

## Architecture Overview

### Microfrontend Strategy
[[LLM: Define the overall microfrontend approach]]

**Architecture Pattern**: {{architecture_pattern}} (Module Federation / Single-SPA / Micro-apps / iFrame)

**Integration Approach**: {{integration_approach}}

**Team Organization**: {{team_organization}}

**Technology Diversity**: {{technology_diversity}}

### System Boundaries
[[LLM: Define the overall frontend system boundaries]]

- **Application Shell**: {{shell_responsibilities}}
- **Shared Services**: {{shared_services}}
- **Module Boundaries**: {{module_boundaries}}
- **External Integrations**: {{external_integrations}}

## Frontend Module Architecture

### Module Definitions
[[LLM: Define each microfrontend module]]

<<REPEAT frontend_module>>
#### {{module_name}} Module

**Purpose**: {{module_purpose}}

**Team Ownership**: {{module_team}}

**Technology Stack**: {{module_tech_stack}}

**Module Boundaries**:
- **Includes**: {{module_includes}}
- **Excludes**: {{module_excludes}}

**Routes Owned**:
- {{route_1}}: {{route_1_description}}
- {{route_2}}: {{route_2_description}}

**Dependencies**:
- Shared Libraries: {{module_shared_deps}}
- External APIs: {{module_api_deps}}
- Other Modules: {{module_cross_deps}}

**Exposed Components**:
- {{exposed_component_1}}: {{component_1_purpose}}
- {{exposed_component_2}}: {{component_2_purpose}}
<<END_REPEAT>>

### Application Shell
[[LLM: Define the application shell responsibilities]]

**Shell Responsibilities**:
- {{shell_responsibility_1}}
- {{shell_responsibility_2}}
- {{shell_responsibility_3}}

**Shell Components**:
- **Navigation**: {{navigation_strategy}}
- **Authentication**: {{auth_integration}}
- **Error Boundaries**: {{error_handling}}
- **Loading States**: {{loading_strategy}}

## Integration Patterns

### Module Federation Configuration
^^CONDITION: architecture_pattern == "Module Federation"^^
[[LLM: Define Module Federation setup]]

**Host Application**: {{host_application}}

**Remote Modules**:
<<REPEAT remote_module>>
- **{{remote_name}}**
  - Entry Point: {{remote_entry}}
  - Exposed Modules: {{remote_exposed}}
  - Shared Dependencies: {{remote_shared}}
<<END_REPEAT>>

**Webpack Configuration**:
```javascript
{{webpack_config}}
```
^^END_CONDITION^^

### Routing Strategy
[[LLM: Define routing coordination across modules]]

**Routing Approach**: {{routing_approach}}

**Route Distribution**:
<<REPEAT route_mapping>>
- **{{route_pattern}}** → {{owning_module}}
  - Component: {{route_component}}
  - Guards: {{route_guards}}
  - Data Loading: {{route_data}}
<<END_REPEAT>>

**Navigation Coordination**:
- **Inter-module Navigation**: {{inter_module_nav}}
- **Deep Linking**: {{deep_linking_strategy}}
- **Browser History**: {{history_management}}

### State Management
[[LLM: Define state management across modules]]

**State Architecture**: {{state_architecture}}

**Shared State**:
- **Global State**: {{global_state_items}}
- **State Synchronization**: {{state_sync_method}}
- **State Persistence**: {{state_persistence}}

**Module-Specific State**:
- **Local State**: {{local_state_strategy}}
- **State Isolation**: {{state_isolation}}
- **Cross-Module Communication**: {{cross_module_comm}}

## Shared Resources

### Design System
[[LLM: Define shared design system and components]]

**Design System**: {{design_system_name}}

**Shared Components**:
<<REPEAT shared_component>>
- **{{component_name}}**
  - Purpose: {{component_purpose}}
  - API: {{component_api}}
  - Styling: {{component_styling}}
  - Versioning: {{component_versioning}}
<<END_REPEAT>>

**Theme Management**:
- **Theme Provider**: {{theme_provider}}
- **CSS Variables**: {{css_variables}}
- **Brand Consistency**: {{brand_consistency}}

### Shared Dependencies
[[LLM: Define shared libraries and dependencies]]

**Shared Libraries**:
<<REPEAT shared_library>>
- **{{library_name}}** ({{library_version}})
  - Purpose: {{library_purpose}}
  - Sharing Strategy: {{library_sharing}}
  - Version Management: {{library_versioning}}
<<END_REPEAT>>

**Dependency Management**:
- **Version Alignment**: {{version_alignment}}
- **Bundle Optimization**: {{bundle_optimization}}
- **Duplicate Prevention**: {{duplicate_prevention}}

## Development Workflow

### Local Development
[[LLM: Define local development setup]]

**Development Setup**:
- **Module Development**: {{module_dev_setup}}
- **Integration Testing**: {{integration_testing}}
- **Hot Reloading**: {{hot_reload_strategy}}
- **Mock Services**: {{mock_services}}

**Development Commands**:
```bash
# Start individual module
{{start_module_command}}

# Start integrated application
{{start_integrated_command}}

# Run tests
{{test_command}}
```

### Build & Deployment
[[LLM: Define build and deployment process]]

**Build Strategy**:
- **Module Builds**: {{module_build_process}}
- **Dependency Resolution**: {{dependency_resolution}}
- **Asset Optimization**: {{asset_optimization}}
- **Bundle Analysis**: {{bundle_analysis}}

**Deployment Pipeline**:
- **Independent Deployment**: {{independent_deployment}}
- **Deployment Coordination**: {{deployment_coordination}}
- **Rollback Strategy**: {{rollback_strategy}}
- **Feature Flags**: {{feature_flags}}

### Testing Strategy
[[LLM: Define testing approach for microfrontends]]

**Testing Levels**:
- **Unit Testing**: {{unit_testing_approach}}
- **Integration Testing**: {{integration_testing_approach}}
- **End-to-End Testing**: {{e2e_testing_approach}}
- **Visual Testing**: {{visual_testing_approach}}

**Cross-Module Testing**:
- **Contract Testing**: {{contract_testing}}
- **Integration Points**: {{integration_testing_points}}
- **Shared Component Testing**: {{shared_component_testing}}

## Performance Optimization

### Loading Strategy
[[LLM: Define module loading and performance optimization]]

**Module Loading**:
- **Lazy Loading**: {{lazy_loading_strategy}}
- **Preloading**: {{preloading_strategy}}
- **Code Splitting**: {{code_splitting}}
- **Bundle Size Optimization**: {{bundle_optimization}}

**Performance Targets**:
- **Initial Load Time**: {{initial_load_target}}
- **Module Load Time**: {{module_load_target}}
- **Bundle Size Limits**: {{bundle_size_limits}}

### Caching Strategy
[[LLM: Define caching approach]]

**Browser Caching**:
- **Static Assets**: {{static_asset_caching}}
- **Module Caching**: {{module_caching}}
- **Cache Invalidation**: {{cache_invalidation}}

**CDN Strategy**:
- **Asset Distribution**: {{cdn_distribution}}
- **Edge Caching**: {{edge_caching}}
- **Geographic Distribution**: {{geo_distribution}}

## Error Handling & Monitoring

### Error Boundaries
[[LLM: Define error handling across modules]]

**Error Isolation**:
- **Module Error Boundaries**: {{module_error_boundaries}}
- **Fallback Components**: {{fallback_components}}
- **Error Recovery**: {{error_recovery}}

**Error Reporting**:
- **Error Tracking**: {{error_tracking_service}}
- **Error Context**: {{error_context}}
- **User Notifications**: {{user_error_notifications}}

### Monitoring & Analytics
[[LLM: Define monitoring approach]]

**Performance Monitoring**:
- **Core Web Vitals**: {{core_web_vitals}}
- **Module Performance**: {{module_performance}}
- **User Experience**: {{user_experience_metrics}}

**Business Analytics**:
- **Feature Usage**: {{feature_usage_tracking}}
- **User Journeys**: {{user_journey_tracking}}
- **A/B Testing**: {{ab_testing_strategy}}

## Security Considerations

### Authentication & Authorization
[[LLM: Define security across modules]]

**Authentication Flow**:
- **SSO Integration**: {{sso_integration}}
- **Token Management**: {{token_management}}
- **Session Handling**: {{session_handling}}

**Authorization**:
- **Role-Based Access**: {{rbac_implementation}}
- **Feature Flags**: {{security_feature_flags}}
- **API Security**: {{api_security}}

### Content Security
[[LLM: Define content security policies]]

**Content Security Policy**:
- **CSP Headers**: {{csp_headers}}
- **Script Sources**: {{script_sources}}
- **Cross-Origin Policies**: {{cross_origin_policies}}

## Migration Strategy

^^CONDITION: is_migration^^
### Migration from Monolith
[[LLM: Define migration strategy from existing frontend]]

**Migration Approach**: {{migration_approach}}

**Migration Phases**:
- **Phase 1**: {{migration_phase_1}}
- **Phase 2**: {{migration_phase_2}}
- **Phase 3**: {{migration_phase_3}}

**Coexistence Strategy**:
- **Gradual Migration**: {{gradual_migration}}
- **Feature Parity**: {{feature_parity}}
- **User Experience**: {{migration_ux}}
^^END_CONDITION^^

---

@{example_microfrontend_spec}
# E-commerce Platform - Microfrontend Architecture Specification

## Architecture Overview

### Microfrontend Strategy
**Architecture Pattern**: Module Federation with Webpack 5

**Integration Approach**: Runtime integration with shared shell application

**Team Organization**: Domain-aligned teams owning specific frontend modules

## Frontend Module Architecture

### Module Definitions

#### Product Catalog Module
**Purpose**: Product browsing, search, and filtering functionality

**Team Ownership**: Product Team

**Technology Stack**: React 18, TypeScript, React Query

**Routes Owned**:
- `/products`: Product listing and search
- `/products/:id`: Product detail pages
- `/categories`: Category browsing

**Exposed Components**:
- ProductCard: Reusable product display component
- SearchBar: Product search functionality

#### Shopping Cart Module
**Purpose**: Cart management and checkout initiation

**Team Ownership**: Commerce Team

**Technology Stack**: Vue 3, TypeScript, Pinia

**Routes Owned**:
- `/cart`: Shopping cart management
- `/checkout`: Checkout process

#### User Account Module
**Purpose**: User authentication, profile, and order history

**Team Ownership**: User Experience Team

**Technology Stack**: React 18, TypeScript, Redux Toolkit

**Routes Owned**:
- `/login`: User authentication
- `/profile`: User profile management
- `/orders`: Order history

### Application Shell
**Shell Responsibilities**:
- Global navigation and header
- Authentication state management
- Error boundary handling
- Module loading coordination

## Integration Patterns

### Module Federation Configuration
**Host Application**: Shell Application

**Remote Modules**:
- **product-catalog**
  - Entry Point: https://cdn.example.com/product-catalog/remoteEntry.js
  - Exposed Modules: ./ProductCatalog, ./ProductCard, ./SearchBar
  - Shared Dependencies: react, react-dom, @company/design-system

- **shopping-cart**
  - Entry Point: https://cdn.example.com/shopping-cart/remoteEntry.js
  - Exposed Modules: ./ShoppingCart, ./CheckoutFlow
  - Shared Dependencies: vue, @company/design-system

### Routing Strategy
**Routing Approach**: Centralized routing with module delegation

**Route Distribution**:
- **/products/*** → Product Catalog Module
- **/cart/*** → Shopping Cart Module
- **/account/*** → User Account Module

### State Management
**State Architecture**: Hybrid approach with global and local state

**Shared State**:
- User authentication status
- Shopping cart contents
- Global notifications

**Cross-Module Communication**: Custom event system for cart updates and user state changes
@{/example_microfrontend_spec}

[[LLM: Process all template markup and create a comprehensive microfrontend specification. Use the example as guidance but create content specific to the user's application. Ensure all modules, integration patterns, and coordination strategies are clearly defined.]]
