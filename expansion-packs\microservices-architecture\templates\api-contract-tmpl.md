# {{service_name}} API Contract

[[LLM: This template creates a comprehensive API contract specification for a microservice. Focus on precise API definitions, versioning, and contract testing. Use OpenAPI/Swagger format where appropriate.]]

## API Overview

### Service Information
[[LLM: Define basic service API information]]

- **Service Name**: {{service_name}}
- **API Version**: {{api_version}}
- **Base URL**: {{base_url}}
- **Protocol**: {{protocol}} (HTTP/HTTPS/gRPC/GraphQL)
- **Authentication**: {{authentication_method}}

### API Design Principles
[[LLM: Document the API design principles followed]]

- **Design Style**: {{api_design_style}}
- **Naming Conventions**: {{naming_conventions}}
- **Error Handling**: {{error_handling_approach}}
- **Versioning Strategy**: {{versioning_strategy}}

## Authentication & Authorization

### Authentication Method
[[LLM: Define how clients authenticate with this API]]

- **Method**: {{auth_method}}
- **Token Format**: {{token_format}}
- **Token Lifetime**: {{token_lifetime}}
- **Refresh Strategy**: {{refresh_strategy}}

### Authorization Model
[[LLM: Define authorization and permissions]]

- **Permission Model**: {{permission_model}}
- **Scopes/Roles**: {{scopes_roles}}
- **Resource Access**: {{resource_access_rules}}

## REST API Endpoints

^^CONDITION: api_style == "REST"^^
### Resource Endpoints
[[LLM: Define REST API endpoints following RESTful principles]]

<<REPEAT rest_endpoint>>
#### {{http_method}} {{endpoint_path}}

**Description**: {{endpoint_description}}

**Parameters**:
- Path Parameters: {{path_parameters}}
- Query Parameters: {{query_parameters}}
- Headers: {{required_headers}}

**Request Body**:
```json
{{request_body_schema}}
```

**Response**:
- **Success ({{success_status_code}})**:
```json
{{success_response_schema}}
```

- **Error Responses**:
  - {{error_status_code_1}}: {{error_description_1}}
  - {{error_status_code_2}}: {{error_description_2}}

**Example Request**:
```bash
curl -X {{http_method}} "{{base_url}}{{endpoint_path}}" \
  -H "Authorization: Bearer {{example_token}}" \
  -H "Content-Type: application/json" \
  -d '{{example_request_body}}'
```

**Example Response**:
```json
{{example_response_body}}
```
<<END_REPEAT>>
^^END_CONDITION^^

## GraphQL Schema

^^CONDITION: api_style == "GraphQL"^^
### Schema Definition
[[LLM: Define GraphQL schema for the service]]

```graphql
{{graphql_schema}}
```

### Query Examples
[[LLM: Provide example GraphQL queries]]

<<REPEAT graphql_query>>
#### {{query_name}}

**Purpose**: {{query_purpose}}

**Query**:
```graphql
{{query_definition}}
```

**Variables**:
```json
{{query_variables}}
```

**Response**:
```json
{{query_response}}
```
<<END_REPEAT>>

### Mutation Examples
[[LLM: Provide example GraphQL mutations]]

<<REPEAT graphql_mutation>>
#### {{mutation_name}}

**Purpose**: {{mutation_purpose}}

**Mutation**:
```graphql
{{mutation_definition}}
```

**Variables**:
```json
{{mutation_variables}}
```

**Response**:
```json
{{mutation_response}}
```
<<END_REPEAT>>
^^END_CONDITION^^

## Event Contracts

^^CONDITION: has_events^^
### Published Events
[[LLM: Define events published by this service]]

<<REPEAT published_event>>
#### {{event_name}}

**Description**: {{event_description}}

**Trigger**: {{event_trigger}}

**Schema**:
```json
{
  "eventType": "{{event_name}}",
  "version": "{{event_version}}",
  "timestamp": "{{timestamp_format}}",
  "source": "{{service_name}}",
  "data": {{event_data_schema}}
}
```

**Example**:
```json
{{event_example}}
```

**Consumers**: {{event_consumers}}
<<END_REPEAT>>

### Consumed Events
[[LLM: Define events consumed by this service]]

<<REPEAT consumed_event>>
#### {{consumed_event_name}}

**Description**: {{consumed_event_description}}

**Source**: {{event_source_service}}

**Handler**: {{event_handler_name}}

**Expected Schema**:
```json
{{consumed_event_schema}}
```

**Processing Logic**: {{event_processing_logic}}
<<END_REPEAT>>
^^END_CONDITION^^

## Data Models

### Core Data Types
[[LLM: Define the main data models used in the API]]

<<REPEAT data_model>>
#### {{model_name}}

**Description**: {{model_description}}

**Schema**:
```json
{{model_schema}}
```

**Validation Rules**:
- {{validation_rule_1}}
- {{validation_rule_2}}
- {{validation_rule_3}}

**Example**:
```json
{{model_example}}
```
<<END_REPEAT>>

### Enum Types
[[LLM: Define enumeration types used in the API]]

<<REPEAT enum_type>>
#### {{enum_name}}

**Description**: {{enum_description}}

**Values**:
- `{{enum_value_1}}`: {{enum_value_1_description}}
- `{{enum_value_2}}`: {{enum_value_2_description}}
- `{{enum_value_3}}`: {{enum_value_3_description}}
<<END_REPEAT>>

## Error Handling

### Error Response Format
[[LLM: Define standard error response format]]

```json
{
  "error": {
    "code": "{{error_code}}",
    "message": "{{error_message}}",
    "details": "{{error_details}}",
    "timestamp": "{{error_timestamp}}",
    "traceId": "{{trace_id}}"
  }
}
```

### Error Codes
[[LLM: Define specific error codes and their meanings]]

<<REPEAT error_code>>
- **{{error_code}}**: {{error_description}}
  - HTTP Status: {{http_status}}
  - Retry Strategy: {{retry_strategy}}
  - Client Action: {{client_action}}
<<END_REPEAT>>

## Rate Limiting

### Rate Limit Policy
[[LLM: Define rate limiting rules]]

- **Rate Limit**: {{rate_limit}}
- **Time Window**: {{time_window}}
- **Rate Limit Headers**: {{rate_limit_headers}}
- **Exceeded Response**: {{rate_limit_exceeded_response}}

## API Versioning

### Versioning Strategy
[[LLM: Define how API versions are managed]]

- **Versioning Method**: {{versioning_method}}
- **Current Version**: {{current_version}}
- **Supported Versions**: {{supported_versions}}
- **Deprecation Policy**: {{deprecation_policy}}

### Version Migration
[[LLM: Define version migration guidelines]]

- **Breaking Changes**: {{breaking_changes_policy}}
- **Migration Timeline**: {{migration_timeline}}
- **Backward Compatibility**: {{backward_compatibility}}

## Testing & Validation

### Contract Testing
[[LLM: Define contract testing approach]]

- **Testing Framework**: {{contract_testing_framework}}
- **Test Scenarios**: {{test_scenarios}}
- **Mock Services**: {{mock_services}}
- **Validation Rules**: {{validation_rules}}

### API Documentation Testing
[[LLM: Define how API documentation is validated]]

- **Documentation Tests**: {{documentation_tests}}
- **Example Validation**: {{example_validation}}
- **Schema Validation**: {{schema_validation}}

## Performance Characteristics

### Performance Targets
[[LLM: Define performance expectations]]

- **Response Time**: {{response_time_target}}
- **Throughput**: {{throughput_target}}
- **Concurrent Requests**: {{concurrent_requests}}
- **Payload Size Limits**: {{payload_limits}}

### Caching Strategy
[[LLM: Define caching behavior]]

- **Cache Headers**: {{cache_headers}}
- **Cache Duration**: {{cache_duration}}
- **Cache Invalidation**: {{cache_invalidation}}

## Monitoring & Observability

### API Metrics
[[LLM: Define key metrics to monitor]]

- **Request Metrics**: {{request_metrics}}
- **Error Metrics**: {{error_metrics}}
- **Performance Metrics**: {{performance_metrics}}
- **Business Metrics**: {{business_metrics}}

### Logging Requirements
[[LLM: Define logging requirements for API calls]]

- **Request Logging**: {{request_logging}}
- **Response Logging**: {{response_logging}}
- **Error Logging**: {{error_logging}}
- **Audit Logging**: {{audit_logging}}

---

@{example_api_contract}
# Product Catalog API Contract

## API Overview

### Service Information
- **Service Name**: Product Catalog Service
- **API Version**: v1.0.0
- **Base URL**: https://api.example.com/catalog/v1
- **Protocol**: HTTPS REST
- **Authentication**: Bearer Token (JWT)

## REST API Endpoints

#### GET /products

**Description**: Retrieve a paginated list of products with optional filtering

**Parameters**:
- Query Parameters: 
  - `page` (integer, default: 1): Page number
  - `limit` (integer, default: 20, max: 100): Items per page
  - `category` (string, optional): Filter by category
  - `search` (string, optional): Search term

**Response**:
- **Success (200)**:
```json
{
  "products": [
    {
      "id": "prod_123",
      "name": "Example Product",
      "price": 29.99,
      "category": "electronics",
      "inStock": true
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "hasNext": true
  }
}
```

#### POST /products

**Description**: Create a new product in the catalog

**Request Body**:
```json
{
  "name": "New Product",
  "description": "Product description",
  "price": 49.99,
  "category": "electronics",
  "inventory": 100
}
```

**Response**:
- **Success (201)**:
```json
{
  "id": "prod_456",
  "name": "New Product",
  "price": 49.99,
  "createdAt": "2024-01-15T10:30:00Z"
}
```
@{/example_api_contract}

[[LLM: Process all template markup and create a comprehensive API contract. Use the example as guidance but create content specific to the user's service API. Ensure all endpoints, data models, and error handling are clearly defined.]]
