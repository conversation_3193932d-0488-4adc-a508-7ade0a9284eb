# {{system_name}} - Distributed System Product Requirements Document

[[LLM: This template creates a comprehensive PRD for distributed systems. Focus on system-wide requirements, service interactions, and cross-cutting concerns. Use advanced elicitation to gather distributed system requirements.]]

## Executive Summary

[[LLM: Create a 2-3 paragraph executive summary covering the distributed system vision, key capabilities, and business value]]

**Product Vision**: {{product_vision}}

**Key Business Outcomes**: {{business_outcomes}}

**System Architecture Approach**: {{architecture_approach}}

## Product Overview

### Business Context
[[LLM: Describe the business context and distributed system rationale]]

- **Market Opportunity**: {{market_opportunity}}
- **Business Problem**: {{business_problem}}
- **Target Users**: {{target_users}}
- **Success Metrics**: {{success_metrics}}

### System Scope
[[LLM: Define the overall system scope and boundaries]]

- **System Boundaries**: {{system_boundaries}}
- **In Scope**: {{in_scope}}
- **Out of Scope**: {{out_of_scope}}
- **Future Considerations**: {{future_scope}}

## User Stories and Epics

### System-Level Epics
[[LLM: Define major epics that span multiple services]]

<<REPEAT system_epic>>
#### Epic: {{epic_name}}

**Description**: {{epic_description}}

**Business Value**: {{epic_business_value}}

**Acceptance Criteria**:
- {{epic_criteria_1}}
- {{epic_criteria_2}}
- {{epic_criteria_3}}

**Services Involved**: {{epic_services}}

**Dependencies**: {{epic_dependencies}}

**Priority**: {{epic_priority}}

**Effort Estimate**: {{epic_effort}}
<<END_REPEAT>>

### Cross-Service User Stories
[[LLM: Define user stories that involve multiple services]]

<<REPEAT cross_service_story>>
#### Story: {{story_title}}

**As a** {{user_type}}
**I want** {{user_want}}
**So that** {{user_benefit}}

**Acceptance Criteria**:
- {{story_criteria_1}}
- {{story_criteria_2}}
- {{story_criteria_3}}

**Service Interactions**:
- {{service_interaction_1}}
- {{service_interaction_2}}

**API Requirements**:
- {{api_requirement_1}}
- {{api_requirement_2}}

**Data Flow**: {{data_flow_description}}

**Priority**: {{story_priority}}

**Effort**: {{story_effort}}
<<END_REPEAT>>

### Service-Specific Epics
[[LLM: Define epics that are primarily within single services]]

<<REPEAT service_epic>>
#### {{service_name}} Service Epic: {{service_epic_name}}

**Description**: {{service_epic_description}}

**Primary Service**: {{primary_service}}

**Supporting Services**: {{supporting_services}}

**User Stories**:
<<REPEAT service_story>>
- **{{service_story_title}}**: {{service_story_description}}
  - Acceptance Criteria: {{service_story_criteria}}
  - API Impact: {{service_story_api_impact}}
  - Data Requirements: {{service_story_data}}
<<END_REPEAT>>

**Priority**: {{service_epic_priority}}

**Dependencies**: {{service_epic_dependencies}}
<<END_REPEAT>>

## System Architecture Requirements

### Service Architecture
[[LLM: Define service-level architecture requirements]]

**Service Decomposition**:
- **Identified Services**: {{identified_services}}
- **Service Boundaries**: {{service_boundaries_rationale}}
- **Service Ownership**: {{service_ownership}}

**Communication Patterns**:
- **Synchronous Communication**: {{sync_communication_requirements}}
- **Asynchronous Communication**: {{async_communication_requirements}}
- **Event-Driven Patterns**: {{event_driven_requirements}}

### Data Architecture
[[LLM: Define data management across services]]

**Data Ownership**:
- **Service Data Ownership**: {{data_ownership_mapping}}
- **Shared Data Strategy**: {{shared_data_approach}}
- **Data Consistency**: {{data_consistency_requirements}}

**Data Flow Requirements**:
- **Cross-Service Data Flow**: {{cross_service_data_flow}}
- **Event Sourcing**: {{event_sourcing_requirements}}
- **Data Synchronization**: {{data_sync_requirements}}

### Integration Requirements
[[LLM: Define integration and interoperability requirements]]

**API Requirements**:
- **API Design Standards**: {{api_design_standards}}
- **API Versioning**: {{api_versioning_requirements}}
- **API Documentation**: {{api_documentation_requirements}}

**External Integrations**:
- **Third-Party Services**: {{third_party_integrations}}
- **Legacy System Integration**: {{legacy_integration_requirements}}
- **Partner APIs**: {{partner_api_requirements}}

## Frontend Architecture (if applicable)

^^CONDITION: has_frontend^^
### Microfrontend Requirements
[[LLM: Define frontend modularity requirements]]

**Frontend Architecture**:
- **Microfrontend Approach**: {{microfrontend_approach}}
- **Module Boundaries**: {{frontend_module_boundaries}}
- **Shared Components**: {{shared_component_requirements}}

**User Experience Requirements**:
- **Consistent UX**: {{ux_consistency_requirements}}
- **Performance**: {{frontend_performance_requirements}}
- **Accessibility**: {{accessibility_requirements}}

**Frontend Integration**:
- **Routing Strategy**: {{routing_requirements}}
- **State Management**: {{state_management_requirements}}
- **Authentication Flow**: {{frontend_auth_requirements}}
^^END_CONDITION^^

## Non-Functional Requirements

### Performance Requirements
[[LLM: Define system-wide performance requirements]]

**Response Time**:
- **API Response Times**: {{api_response_time_requirements}}
- **End-to-End Latency**: {{e2e_latency_requirements}}
- **Real-Time Requirements**: {{real_time_requirements}}

**Throughput**:
- **Request Volume**: {{request_volume_requirements}}
- **Data Processing**: {{data_processing_requirements}}
- **Concurrent Users**: {{concurrent_user_requirements}}

### Scalability Requirements
[[LLM: Define scalability needs across the system]]

**Horizontal Scaling**:
- **Service Scaling**: {{service_scaling_requirements}}
- **Database Scaling**: {{database_scaling_requirements}}
- **Auto-Scaling**: {{auto_scaling_requirements}}

**Growth Planning**:
- **User Growth**: {{user_growth_projections}}
- **Data Growth**: {{data_growth_projections}}
- **Geographic Expansion**: {{geographic_scaling_requirements}}

### Reliability Requirements
[[LLM: Define reliability and availability requirements]]

**Availability**:
- **System Availability**: {{system_availability_target}}
- **Service Availability**: {{service_availability_targets}}
- **Downtime Tolerance**: {{downtime_tolerance}}

**Fault Tolerance**:
- **Failure Handling**: {{failure_handling_requirements}}
- **Circuit Breakers**: {{circuit_breaker_requirements}}
- **Graceful Degradation**: {{graceful_degradation_requirements}}

**Disaster Recovery**:
- **Backup Requirements**: {{backup_requirements}}
- **Recovery Time**: {{recovery_time_objectives}}
- **Data Loss Tolerance**: {{data_loss_tolerance}}

### Security Requirements
[[LLM: Define security requirements across the distributed system]]

**Authentication & Authorization**:
- **User Authentication**: {{user_auth_requirements}}
- **Service-to-Service Auth**: {{service_auth_requirements}}
- **Authorization Model**: {{authorization_requirements}}

**Data Protection**:
- **Data Encryption**: {{encryption_requirements}}
- **PII Protection**: {{pii_protection_requirements}}
- **Compliance**: {{compliance_requirements}}

**Network Security**:
- **API Security**: {{api_security_requirements}}
- **Network Isolation**: {{network_security_requirements}}
- **Threat Protection**: {{threat_protection_requirements}}

## Operational Requirements

### Monitoring & Observability
[[LLM: Define monitoring requirements across services]]

**System Monitoring**:
- **Service Health**: {{service_health_monitoring}}
- **Performance Monitoring**: {{performance_monitoring_requirements}}
- **Business Metrics**: {{business_metrics_requirements}}

**Logging & Tracing**:
- **Distributed Tracing**: {{distributed_tracing_requirements}}
- **Log Aggregation**: {{log_aggregation_requirements}}
- **Audit Logging**: {{audit_logging_requirements}}

### Deployment & Operations
[[LLM: Define deployment and operational requirements]]

**Deployment Strategy**:
- **Deployment Model**: {{deployment_model_requirements}}
- **Release Strategy**: {{release_strategy_requirements}}
- **Rollback Capabilities**: {{rollback_requirements}}

**Operational Procedures**:
- **Incident Response**: {{incident_response_requirements}}
- **Maintenance Procedures**: {{maintenance_requirements}}
- **Capacity Planning**: {{capacity_planning_requirements}}

## Development & Team Requirements

### Development Workflow
[[LLM: Define development process requirements]]

**Team Structure**:
- **Service Ownership**: {{team_ownership_requirements}}
- **Cross-Team Coordination**: {{cross_team_coordination}}
- **Shared Responsibilities**: {{shared_responsibility_requirements}}

**Development Process**:
- **Independent Development**: {{independent_development_requirements}}
- **Integration Testing**: {{integration_testing_requirements}}
- **Contract Testing**: {{contract_testing_requirements}}

### Technology Requirements
[[LLM: Define technology stack requirements]]

**Technology Choices**:
- **Service Technologies**: {{service_technology_requirements}}
- **Database Technologies**: {{database_technology_requirements}}
- **Infrastructure Technologies**: {{infrastructure_technology_requirements}}

**Standards & Guidelines**:
- **Coding Standards**: {{coding_standards_requirements}}
- **API Standards**: {{api_standards_requirements}}
- **Documentation Standards**: {{documentation_standards_requirements}}

## Success Criteria & Metrics

### Business Metrics
[[LLM: Define business success metrics]]

**Key Performance Indicators**:
- {{business_kpi_1}}: {{kpi_1_target}}
- {{business_kpi_2}}: {{kpi_2_target}}
- {{business_kpi_3}}: {{kpi_3_target}}

**User Experience Metrics**:
- {{ux_metric_1}}: {{ux_metric_1_target}}
- {{ux_metric_2}}: {{ux_metric_2_target}}

### Technical Metrics
[[LLM: Define technical success metrics]]

**System Performance**:
- {{performance_metric_1}}: {{performance_target_1}}
- {{performance_metric_2}}: {{performance_target_2}}

**Operational Metrics**:
- {{operational_metric_1}}: {{operational_target_1}}
- {{operational_metric_2}}: {{operational_target_2}}

## Implementation Roadmap

### Phase Planning
[[LLM: Define implementation phases]]

**Phase 1: {{phase_1_name}}**
- Duration: {{phase_1_duration}}
- Scope: {{phase_1_scope}}
- Services: {{phase_1_services}}
- Success Criteria: {{phase_1_success}}

**Phase 2: {{phase_2_name}}**
- Duration: {{phase_2_duration}}
- Scope: {{phase_2_scope}}
- Services: {{phase_2_services}}
- Success Criteria: {{phase_2_success}}

**Phase 3: {{phase_3_name}}**
- Duration: {{phase_3_duration}}
- Scope: {{phase_3_scope}}
- Services: {{phase_3_services}}
- Success Criteria: {{phase_3_success}}

### Risk Assessment
[[LLM: Identify and assess implementation risks]]

<<REPEAT implementation_risk>>
**Risk**: {{risk_description}}
- **Impact**: {{risk_impact}}
- **Probability**: {{risk_probability}}
- **Mitigation**: {{risk_mitigation_strategy}}
- **Contingency**: {{risk_contingency_plan}}
<<END_REPEAT>>

---

@{example_distributed_prd}
# E-commerce Platform - Distributed System PRD

## Executive Summary

**Product Vision**: Transform our monolithic e-commerce platform into a scalable, distributed system that supports multiple sales channels, enables rapid feature development, and scales to handle 10x growth.

**Key Business Outcomes**: 
- Reduce time-to-market for new features by 50%
- Support 1M+ concurrent users
- Enable expansion to mobile and B2B channels
- Improve system reliability to 99.9% uptime

## System-Level Epics

#### Epic: Multi-Channel Order Processing

**Description**: Enable order processing across web, mobile, and B2B channels with consistent business rules and real-time inventory management.

**Business Value**: Support business expansion to new channels and customer segments

**Services Involved**: Order Service, Inventory Service, Payment Service, Notification Service

**Acceptance Criteria**:
- Orders can be placed from any channel
- Inventory is updated in real-time across channels
- Order status is synchronized across all touchpoints
- Business rules are consistently applied

#### Epic: Real-Time Personalization

**Description**: Provide personalized product recommendations and content across all customer touchpoints using real-time user behavior and preferences.

**Business Value**: Increase conversion rates and average order value

**Services Involved**: User Service, Product Catalog Service, Recommendation Service, Analytics Service

**Cross-Service User Stories**:

#### Story: Unified Shopping Cart

**As a** customer
**I want** my shopping cart to be synchronized across all devices and channels
**So that** I can start shopping on one device and complete on another

**Service Interactions**:
- User Service provides authentication and user context
- Cart Service manages cart state and synchronization
- Product Catalog Service provides product details and availability

**API Requirements**:
- Real-time cart synchronization APIs
- Cross-device session management
- Inventory validation APIs

## Non-Functional Requirements

### Performance Requirements
- **API Response Times**: 95th percentile < 200ms for read operations, < 500ms for write operations
- **End-to-End Latency**: Page load times < 2 seconds
- **Concurrent Users**: Support 100,000 concurrent active users

### Scalability Requirements
- **Service Scaling**: Each service must support independent horizontal scaling
- **Auto-Scaling**: Automatic scaling based on CPU, memory, and request volume
- **Geographic Expansion**: Support for multi-region deployment

### Security Requirements
- **User Authentication**: OAuth 2.0 with JWT tokens
- **Service-to-Service Auth**: mTLS for internal service communication
- **Data Protection**: PCI DSS compliance for payment data, GDPR compliance for user data
@{/example_distributed_prd}

[[LLM: Process all template markup and create a comprehensive distributed system PRD. Use the example as guidance but create content specific to the user's system. Ensure all epics, stories, and requirements are clearly defined with service interactions.]]
