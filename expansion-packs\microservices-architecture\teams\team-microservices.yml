bundle:
  name: Team Microservices
  description: Specialized team for distributed system architecture and microservices development. Includes system-level architects, API designers, and microfrontend specialists for complex distributed applications requiring service decomposition, API design, and frontend modularity.

agents:
  - bmad-orchestrator
  - analyst
  - pm
  - microservices-architect
  - api-designer
  - microfrontend-architect
  - architect
  - po
  - dev
  - qa

workflows:
  - microservices-greenfield
  - service-addition
  - microfrontend-integration
  - greenfield-fullstack
  - brownfield-fullstack
  - greenfield-service
  - brownfield-service
