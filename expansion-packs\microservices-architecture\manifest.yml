name: microservices-architecture
version: 1.0.0
description: Microservices Architecture expansion pack for BMAD Method - Distributed systems and microfrontend development following Architecture_stack/Microservices_Architecture.md framework
author: BMAD Team
bmad_version_compatibility: ">=4.0.0"

# Expansion pack metadata
metadata:
  category: architecture
  complexity: advanced
  target_systems:
    - distributed-web-applications
    - microservices-backend
    - microfrontend-architecture
    - event-driven-systems
  integration_level: full

# Files to install and their destinations
files:
  # Specialized Agent Definitions
  - source: agents/microservices-architect.md
    destination: bmad-core/agents/microservices-architect.md
    
  - source: agents/api-designer.md
    destination: bmad-core/agents/api-designer.md
    
  - source: agents/microfrontend-architect.md
    destination: bmad-core/agents/microfrontend-architect.md

  # Document Templates
  - source: templates/distributed-system-brief-tmpl.md
    destination: bmad-core/templates/distributed-system-brief-tmpl.md
    
  - source: templates/microservice-brief-tmpl.md
    destination: bmad-core/templates/microservice-brief-tmpl.md
    
  - source: templates/distributed-prd-tmpl.md
    destination: bmad-core/templates/distributed-prd-tmpl.md
    
  - source: templates/service-prd-tmpl.md
    destination: bmad-core/templates/service-prd-tmpl.md
    
  - source: templates/microfrontend-spec-tmpl.md
    destination: bmad-core/templates/microfrontend-spec-tmpl.md
    
  - source: templates/api-contract-tmpl.md
    destination: bmad-core/templates/api-contract-tmpl.md
    
  - source: templates/service-architecture-tmpl.md
    destination: bmad-core/templates/service-architecture-tmpl.md
    
  - source: templates/system-integration-tmpl.md
    destination: bmad-core/templates/system-integration-tmpl.md

  # Workflow Definitions
  - source: workflows/microservices-greenfield.yml
    destination: bmad-core/workflows/microservices-greenfield.yml
    
  - source: workflows/service-addition.yml
    destination: bmad-core/workflows/service-addition.yml
    
  - source: workflows/microfrontend-integration.yml
    destination: bmad-core/workflows/microfrontend-integration.yml

  # Specialized Tasks
  - source: tasks/define-service-boundaries.md
    destination: bmad-core/tasks/define-service-boundaries.md
    
  - source: tasks/design-api-contracts.md
    destination: bmad-core/tasks/design-api-contracts.md
    
  - source: tasks/plan-data-consistency.md
    destination: bmad-core/tasks/plan-data-consistency.md
    
  - source: tasks/create-deployment-strategy.md
    destination: bmad-core/tasks/create-deployment-strategy.md
    
  - source: tasks/design-microfrontend-architecture.md
    destination: bmad-core/tasks/design-microfrontend-architecture.md

  # Quality Checklists
  - source: checklists/service-boundary-checklist.md
    destination: bmad-core/checklists/service-boundary-checklist.md
    
  - source: checklists/api-contract-checklist.md
    destination: bmad-core/checklists/api-contract-checklist.md
    
  - source: checklists/microservice-readiness-checklist.md
    destination: bmad-core/checklists/microservice-readiness-checklist.md
    
  - source: checklists/system-integration-checklist.md
    destination: bmad-core/checklists/system-integration-checklist.md
    
  - source: checklists/microfrontend-integration-checklist.md
    destination: bmad-core/checklists/microfrontend-integration-checklist.md

# Team configurations to update (add microservices agents)
team_updates:
  - team: team-all.yml
    add_agents:
      - microservices-architect
      - api-designer
      - microfrontend-architect

# New team configuration for microservices focus
new_teams:
  - name: team-microservices
    description: "Specialized team for distributed system architecture and microservices development. Includes system-level architects, API designers, and microfrontend specialists for complex distributed applications."
    agents:
      - bmad-orchestrator
      - analyst
      - pm
      - microservices-architect
      - api-designer
      - microfrontend-architect
      - architect
      - po
      - dev
      - qa
    workflows:
      - microservices-greenfield
      - service-addition
      - microfrontend-integration
      - greenfield-fullstack
      - brownfield-fullstack

# Dependencies on core BMAD components
dependencies:
  core_agents:
    - analyst # System analysis and requirements
    - pm # Product management for distributed systems
    - architect # Base architecture capabilities
    - ux-expert # Frontend architecture coordination
    - po # Story validation across services
    - dev # Implementation guidance
  core_tasks:
    - create-doc # Template processing
    - advanced-elicitation # Requirements gathering
  core_templates:
    - architecture-tmpl # Base architecture patterns
    - prd-tmpl # Product requirements foundation
  core_workflows:
    - greenfield-fullstack # Integration with existing workflows
    - brownfield-fullstack # Enhancement workflows
  architecture_documentation:
    - Architecture_stack/Microservices_Architecture.md # Comprehensive microservices architecture framework

# Post-install instructions
post_install_message: |
  Microservices Architecture expansion pack installed successfully!

  This expansion pack implements the Enhanced Microservices Architecture framework
  from Architecture_stack/Microservices_Architecture.md with specialized agents,
  workflows, and templates.

  New specialized agents available:
  - microservices-architect: System-level distributed architecture design following Architecture_stack patterns
  - api-designer: Service contracts using three pillars of communication (sync, async, event streaming)
  - microfrontend-architect: Frontend module coordination aligned with backend service boundaries

  New workflows available:
  - microservices-greenfield: Complete distributed system development with organic system thinking
  - service-addition: Adding services to existing systems with Conway's Law considerations
  - microfrontend-integration: Frontend module coordination with value stream alignment

  New team configuration:
  - team-microservices: Specialized distributed systems team following Architecture_stack principles

  To get started:
  1. Review Architecture_stack/Microservices_Architecture.md for foundational concepts
  2. Use '*agent microservices-architect' for system-level planning with DDD principles
  3. Use '*workflow microservices-greenfield' for new distributed systems
  4. Use '*agent api-designer' for service contract design following three pillars
  5. Use '*agent microfrontend-architect' for frontend module planning

  Integration examples and troubleshooting guide available in README.md
