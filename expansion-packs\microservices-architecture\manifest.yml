name: microservices-architecture
version: 1.0.0
description: Microservices Architecture expansion pack for BMAD Method - Distributed systems and microfrontend development
author: BMAD Team
bmad_version_compatibility: ">=4.0.0"

# Expansion pack metadata
metadata:
  category: architecture
  complexity: advanced
  target_systems:
    - distributed-web-applications
    - microservices-backend
    - microfrontend-architecture
    - event-driven-systems
  integration_level: full

# Files to install and their destinations
files:
  # Specialized Agent Definitions
  - source: agents/microservices-architect.md
    destination: bmad-core/agents/microservices-architect.md
    
  - source: agents/api-designer.md
    destination: bmad-core/agents/api-designer.md
    
  - source: agents/microfrontend-architect.md
    destination: bmad-core/agents/microfrontend-architect.md

  # Document Templates
  - source: templates/distributed-system-brief-tmpl.md
    destination: bmad-core/templates/distributed-system-brief-tmpl.md
    
  - source: templates/microservice-brief-tmpl.md
    destination: bmad-core/templates/microservice-brief-tmpl.md
    
  - source: templates/distributed-prd-tmpl.md
    destination: bmad-core/templates/distributed-prd-tmpl.md
    
  - source: templates/service-prd-tmpl.md
    destination: bmad-core/templates/service-prd-tmpl.md
    
  - source: templates/microfrontend-spec-tmpl.md
    destination: bmad-core/templates/microfrontend-spec-tmpl.md
    
  - source: templates/api-contract-tmpl.md
    destination: bmad-core/templates/api-contract-tmpl.md
    
  - source: templates/service-architecture-tmpl.md
    destination: bmad-core/templates/service-architecture-tmpl.md
    
  - source: templates/system-integration-tmpl.md
    destination: bmad-core/templates/system-integration-tmpl.md

  # Workflow Definitions
  - source: workflows/microservices-greenfield.yml
    destination: bmad-core/workflows/microservices-greenfield.yml
    
  - source: workflows/service-addition.yml
    destination: bmad-core/workflows/service-addition.yml
    
  - source: workflows/microfrontend-integration.yml
    destination: bmad-core/workflows/microfrontend-integration.yml

  # Specialized Tasks
  - source: tasks/define-service-boundaries.md
    destination: bmad-core/tasks/define-service-boundaries.md
    
  - source: tasks/design-api-contracts.md
    destination: bmad-core/tasks/design-api-contracts.md
    
  - source: tasks/plan-data-consistency.md
    destination: bmad-core/tasks/plan-data-consistency.md
    
  - source: tasks/create-deployment-strategy.md
    destination: bmad-core/tasks/create-deployment-strategy.md
    
  - source: tasks/design-microfrontend-architecture.md
    destination: bmad-core/tasks/design-microfrontend-architecture.md

  # Quality Checklists
  - source: checklists/service-boundary-checklist.md
    destination: bmad-core/checklists/service-boundary-checklist.md
    
  - source: checklists/api-contract-checklist.md
    destination: bmad-core/checklists/api-contract-checklist.md
    
  - source: checklists/microservice-readiness-checklist.md
    destination: bmad-core/checklists/microservice-readiness-checklist.md
    
  - source: checklists/system-integration-checklist.md
    destination: bmad-core/checklists/system-integration-checklist.md
    
  - source: checklists/microfrontend-integration-checklist.md
    destination: bmad-core/checklists/microfrontend-integration-checklist.md

# Team configurations to update (add microservices agents)
team_updates:
  - team: team-all.yml
    add_agents:
      - microservices-architect
      - api-designer
      - microfrontend-architect

# New team configuration for microservices focus
new_teams:
  - name: team-microservices
    description: "Specialized team for distributed system architecture and microservices development. Includes system-level architects, API designers, and microfrontend specialists for complex distributed applications."
    agents:
      - bmad-orchestrator
      - analyst
      - pm
      - microservices-architect
      - api-designer
      - microfrontend-architect
      - architect
      - po
      - dev
      - qa
    workflows:
      - microservices-greenfield
      - service-addition
      - microfrontend-integration
      - greenfield-fullstack
      - brownfield-fullstack

# Dependencies on core BMAD components
dependencies:
  core_agents:
    - analyst # System analysis and requirements
    - pm # Product management for distributed systems
    - architect # Base architecture capabilities
    - ux-expert # Frontend architecture coordination
    - po # Story validation across services
    - dev # Implementation guidance
  core_tasks:
    - create-doc # Template processing
    - advanced-elicitation # Requirements gathering
  core_templates:
    - architecture-tmpl # Base architecture patterns
    - prd-tmpl # Product requirements foundation
  core_workflows:
    - greenfield-fullstack # Integration with existing workflows
    - brownfield-fullstack # Enhancement workflows

# Post-install instructions
post_install_message: |
  Microservices Architecture expansion pack installed successfully!

  New specialized agents available:
  - microservices-architect: System-level distributed architecture design
  - api-designer: Service contracts and API specifications  
  - microfrontend-architect: Frontend module coordination

  New workflows available:
  - microservices-greenfield: Complete distributed system development
  - service-addition: Adding services to existing systems
  - microfrontend-integration: Frontend module coordination

  New team configuration:
  - team-microservices: Specialized distributed systems team

  To get started:
  1. Use '*agent microservices-architect' for system-level planning
  2. Use '*workflow microservices-greenfield' for new distributed systems
  3. Use '*agent api-designer' for service contract design
  4. Use '*agent microfrontend-architect' for frontend module planning

  Integration examples and troubleshooting guide available in README.md
