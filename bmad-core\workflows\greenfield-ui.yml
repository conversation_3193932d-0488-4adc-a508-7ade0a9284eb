workflow:
  id: greenfield-ui
  name: Greenfield UI/Frontend Development
  description: >-
    Agent workflow for building frontend applications from concept to development.
    Supports both comprehensive planning for complex UIs and rapid prototyping for simple interfaces.
  type: greenfield
  project_types:
    - spa
    - mobile-app
    - micro-frontend
    - static-site
    - ui-prototype
    - simple-interface

  # For Complex UIs (Production Apps, Multiple Views)
  complex_ui_sequence:
    - agent: analyst
      creates: project-brief.md
      optional_steps:
        - brainstorming_session
        - market_research_prompt
      notes: "Can do brainstorming first, then optional deep research before creating project brief. SAVE OUTPUT: Copy final project-brief.md to your project's docs/ folder."

    - agent: pm
      creates: prd.md
      requires: project-brief.md
      notes: "Creates PRD from project brief using prd-tmpl, focused on UI/frontend requirements. SAVE OUTPUT: Copy final prd.md to your project's docs/ folder."

    - agent: ux-expert
      creates: front-end-spec.md
      requires: prd.md
      optional_steps:
        - user_research_prompt
      notes: "Creates UI/UX specification using front-end-spec-tmpl. SAVE OUTPUT: Copy final front-end-spec.md to your project's docs/ folder."

    - agent: ux-expert
      creates: v0_prompt (optional)
      requires: front-end-spec.md
      condition: user_wants_ai_generation
      notes: "OPTIONAL BUT RECOMMENDED: Generate AI UI prompt for tools like v0, Lovable, etc. Use the generate-ai-frontend-prompt task. User can then generate UI in external tool and download project structure."

    - agent: architect
      creates: front-end-architecture.md
      requires: front-end-spec.md
      optional_steps:
        - technical_research_prompt
        - review_generated_ui_structure
      notes: "Creates frontend architecture using front-end-architecture-tmpl. If user generated UI with v0/Lovable, can incorporate the project structure into architecture. May suggest changes to PRD stories or new stories. SAVE OUTPUT: Copy final front-end-architecture.md to your project's docs/ folder."

    - agent: pm
      updates: prd.md (if needed)
      requires: front-end-architecture.md
      condition: architecture_suggests_prd_changes
      notes: "If architect suggests story changes, update PRD and re-export the complete unredacted prd.md to docs/ folder."

    - agent: po
      validates: all_artifacts
      uses: po-master-checklist
      notes: "Validates all documents for consistency and completeness. May require updates to any document."

    - agent: various
      updates: any_flagged_documents
      condition: po_checklist_issues
      notes: "If PO finds issues, return to relevant agent to fix and re-export updated documents to docs/ folder."

    - project_setup_guidance:
      action: guide_project_structure
      condition: user_has_generated_ui
      notes: "If user generated UI with v0/Lovable: For polyrepo setup, place downloaded project in separate frontend repo. For monorepo, place in apps/web or frontend/ directory. Review architecture document for specific guidance."

    - workflow_end:
      action: move_to_ide
      notes: "All planning artifacts complete. Move to IDE environment to begin development. Explain to the user the IDE Development Workflow next steps: data#bmad-kb:IDE Development Workflow"

  # For Simple UIs (Simple Interfaces, Few Components)
  simple_ui_sequence:
    - step: ui_scope
      action: assess complexity
      notes: "First, assess if this needs full planning (use complex_ui_sequence) or can be a simple interface."

    - agent: analyst
      creates: project-brief.md
      optional_steps:
        - brainstorming_session
      notes: "Creates focused project brief for simple UI. SAVE OUTPUT: Copy final project-brief.md to your project's docs/ folder."

    - agent: ux-expert
      creates: simple_wireframes OR quick_spec
      uses: create-epic OR create-story
      requires: project-brief.md
      notes: "Create simple wireframes and component list instead of full UI/UX spec for rapid development."

    - workflow_end:
      action: move_to_ide
      notes: "Simple UI defined. Move to IDE environment to begin development. Explain to the user the IDE Development Workflow next steps: data#bmad-kb:IDE Development Workflow"

  flow_diagram: |
    ```mermaid
    graph TD
        A[Start: UI Development] --> B{UI Complexity?}
        B -->|Complex/Production| C[analyst: project-brief.md]
        B -->|Simple/Prototype| D[analyst: focused project-brief.md]
        
        C --> E[pm: prd.md]
        E --> F[ux-expert: front-end-spec.md]
        F --> F2{Generate v0 prompt?}
        F2 -->|Yes| F3[ux-expert: create v0 prompt]
        F2 -->|No| G[architect: front-end-architecture.md]
        F3 --> F4[User: generate UI in v0/Lovable]
        F4 --> G
        G --> H{Architecture suggests PRD changes?}
        H -->|Yes| I[pm: update prd.md]
        H -->|No| J[po: validate all artifacts]
        I --> J
        J --> K{PO finds issues?}
        K -->|Yes| L[Return to relevant agent for fixes]
        K -->|No| M[Move to IDE Environment]
        L --> J
        
        D --> N[ux-expert: simple wireframes]
        N --> O[Move to IDE Environment]
        
        C -.-> C1[Optional: brainstorming]
        C -.-> C2[Optional: market research]
        F -.-> F1[Optional: user research]
        G -.-> G1[Optional: technical research]
        D -.-> D1[Optional: brainstorming]
        
        style M fill:#90EE90
        style O fill:#90EE90
        style F3 fill:#E6E6FA
        style F4 fill:#E6E6FA
        style C fill:#FFE4B5
        style E fill:#FFE4B5
        style F fill:#FFE4B5
        style G fill:#FFE4B5
        style D fill:#FFB6C1
        style N fill:#FFB6C1
    ```

  decision_guidance:
    use_complex_sequence_when:
      - Building production frontend applications
      - Multiple views/pages with complex interactions
      - Need comprehensive UI/UX design and testing
      - Multiple team members will be involved
      - Long-term maintenance expected
      - Customer-facing applications

    use_simple_sequence_when:
      - Building simple interfaces or prototypes
      - Few views with straightforward interactions
      - Internal tools or admin interfaces
      - Solo developer or small team
      - Quick experiments or proof-of-concepts
      - Learning or educational projects

  handoff_prompts:
    # Complex sequence prompts
    analyst_to_pm: "Project brief is complete. Save it as docs/project-brief.md in your project, then create the PRD."
    pm_to_ux: "PRD is ready. Save it as docs/prd.md in your project, then create the UI/UX specification."
    ux_to_architect: "UI/UX spec complete. Save it as docs/front-end-spec.md in your project, then create the frontend architecture."
    architect_review: "Frontend architecture complete. Save it as docs/front-end-architecture.md. Do you suggest any changes to the PRD stories or need new stories added?"
    architect_to_pm: "Please update the PRD with the suggested story changes, then re-export the complete prd.md to docs/."
    updated_to_po: "All documents ready in docs/ folder. Please validate all artifacts for consistency."
    po_issues: "PO found issues with [document]. Please return to [agent] to fix and re-save the updated document."
    complex_complete: "All planning artifacts validated and saved in docs/ folder. Move to IDE environment to begin development."

    # Simple sequence prompts
    simple_analyst_to_ux: "Focused project brief complete. Save it as docs/project-brief.md, then create simple wireframes for rapid development."
    simple_complete: "Simple UI defined. Move to IDE environment to begin development."
